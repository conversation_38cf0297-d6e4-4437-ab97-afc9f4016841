#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强交易逻辑 - 添加停牌和一字涨跌停检测
"""

import pandas as pd
import numpy as np

class EnhancedTradingLogic:
    """增强的交易逻辑类"""
    
    def __init__(self, limit_threshold=0.095):
        """
        初始化
        
        Args:
            limit_threshold: 涨跌停阈值，默认9.5%（考虑到浮点数精度）
        """
        self.limit_threshold = limit_threshold
    
    def is_suspended(self, df, index):
        """
        判断股票是否停牌
        
        Args:
            df: 股票数据DataFrame
            index: 数据索引
            
        Returns:
            bool: True表示停牌
        """
        row = df.iloc[index]
        
        # 停牌判断条件：
        # 1. 成交量为0
        # 2. 开盘价=最高价=最低价=收盘价（一字停牌）
        # 3. 价格为0或异常
        
        volume_zero = row.get('volume', 0) == 0
        
        # 检查是否一字停牌（所有价格相等）
        prices = [row['open'], row['high'], row['low'], row['close']]
        all_prices_equal = len(set(prices)) == 1
        
        # 检查价格是否异常
        price_invalid = any(p <= 0 for p in prices)
        
        return volume_zero or (all_prices_equal and volume_zero) or price_invalid
    
    def is_limit_up(self, df, index):
        """
        判断是否一字涨停
        
        Args:
            df: 股票数据DataFrame
            index: 数据索引
            
        Returns:
            bool: True表示一字涨停
        """
        if index == 0:
            return False
            
        current_row = df.iloc[index]
        prev_row = df.iloc[index - 1]
        
        # 计算涨幅
        price_change = (current_row['close'] - prev_row['close']) / prev_row['close']
        
        # 一字涨停判断：
        # 1. 涨幅接近涨停幅度（9.5%以上）
        # 2. 开盘价=最高价=最低价=收盘价
        # 3. 有成交量（区别于停牌）
        
        is_limit_price = price_change >= self.limit_threshold
        
        prices = [current_row['open'], current_row['high'], current_row['low'], current_row['close']]
        all_prices_equal = len(set([round(p, 2) for p in prices])) == 1  # 考虑浮点数精度
        
        has_volume = current_row.get('volume', 0) > 0
        
        return is_limit_price and all_prices_equal and has_volume
    
    def is_limit_down(self, df, index):
        """
        判断是否一字跌停
        
        Args:
            df: 股票数据DataFrame
            index: 数据索引
            
        Returns:
            bool: True表示一字跌停
        """
        if index == 0:
            return False
            
        current_row = df.iloc[index]
        prev_row = df.iloc[index - 1]
        
        # 计算跌幅
        price_change = (current_row['close'] - prev_row['close']) / prev_row['close']
        
        # 一字跌停判断：
        # 1. 跌幅接近跌停幅度（-9.5%以下）
        # 2. 开盘价=最高价=最低价=收盘价
        # 3. 有成交量（区别于停牌）
        
        is_limit_price = price_change <= -self.limit_threshold
        
        prices = [current_row['open'], current_row['high'], current_row['low'], current_row['close']]
        all_prices_equal = len(set([round(p, 2) for p in prices])) == 1  # 考虑浮点数精度
        
        has_volume = current_row.get('volume', 0) > 0
        
        return is_limit_price and all_prices_equal and has_volume
    
    def can_buy(self, df, index):
        """
        判断是否可以买入
        
        Args:
            df: 股票数据DataFrame
            index: 数据索引
            
        Returns:
            bool: True表示可以买入
        """
        # 不能买入的情况：
        # 1. 停牌
        # 2. 一字涨停
        
        if self.is_suspended(df, index):
            return False
            
        if self.is_limit_up(df, index):
            return False
            
        return True
    
    def can_sell(self, df, index):
        """
        判断是否可以卖出
        
        Args:
            df: 股票数据DataFrame
            index: 数据索引
            
        Returns:
            bool: True表示可以卖出
        """
        # 不能卖出的情况：
        # 1. 停牌
        # 2. 一字跌停
        
        if self.is_suspended(df, index):
            return False
            
        if self.is_limit_down(df, index):
            return False
            
        return True
    
    def find_next_tradable_day(self, df, start_index, action='buy'):
        """
        寻找下一个可交易日
        
        Args:
            df: 股票数据DataFrame
            start_index: 开始搜索的索引
            action: 交易动作，'buy'或'sell'
            
        Returns:
            int: 可交易日的索引，如果没找到返回None
        """
        for i in range(start_index, len(df)):
            if action == 'buy' and self.can_buy(df, i):
                return i
            elif action == 'sell' and self.can_sell(df, i):
                return i
        
        return None


def create_enhanced_backtest_method():
    """创建增强的回测方法"""
    
    enhanced_code = '''
def backtest_strategy_enhanced(self, df, stock_code):
    """
    增强版回测策略 - 包含停牌和一字涨跌停处理
    
    Args:
        df: 包含信号的股票数据
        stock_code: 股票代码
        
    Returns:
        dict: 回测结果
    """
    # 初始化回测结果
    results = {
        'stock_code': stock_code,
        'total_trades': 0,
        'winning_trades': 0,
        'losing_trades': 0,
        'total_profit': 0.0,
        'max_drawdown': 0.0,
        'trades': []
    }
    
    # 创建增强交易逻辑实例
    trading_logic = EnhancedTradingLogic()
    
    # 遍历数据
    position = None
    entry_price = 0
    entry_date = None
    
    for i in range(len(df)):
        current_row = df.iloc[i]
        current_date = current_row.name
        
        # 检查是否可以交易
        if position is None:  # 没有持仓
            if current_row['signal'] == 1 and trading_logic.can_buy(df, i):
                # 买入信号
                position = 'long'
                entry_price = current_row['close']
                entry_date = current_date
                results['total_trades'] += 1
                
        else:  # 有持仓
            if current_row['signal'] == -1 and trading_logic.can_sell(df, i):
                # 卖出信号
                exit_price = current_row['close']
                profit = (exit_price - entry_price) / entry_price
                
                # 更新回测结果
                results['total_profit'] += profit
                if profit > 0:
                    results['winning_trades'] += 1
                else:
                    results['losing_trades'] += 1
                
                # 记录交易
                trade = {
                    'entry_date': entry_date,
                    'exit_date': current_date,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'profit': profit
                }
                results['trades'].append(trade)
                
                # 重置持仓
                position = None
                entry_price = 0
                entry_date = None
    
    # 计算胜率
    if results['total_trades'] > 0:
        results['win_rate'] = results['winning_trades'] / results['total_trades']
    else:
        results['win_rate'] = 0.0
    
    return results
'''
    return enhanced_code


def main():
    """主函数 - 演示增强交易逻辑"""
    print("🚀 增强交易逻辑 - 停牌和一字涨跌停处理")
    print("=" * 60)
    
    # 创建交易逻辑实例
    trading_logic = EnhancedTradingLogic()
    
    print("✅ 功能特性:")
    print("   1. 停牌检测 - 成交量为0或价格异常")
    print("   2. 一字涨停检测 - 涨幅≥9.5%且四价相等")
    print("   3. 一字跌停检测 - 跌幅≤-9.5%且四价相等")
    print("   4. 延期交易 - 等待可交易日再执行")
    
    print("\n🔧 交易规则:")
    print("   • 一字涨停不买入 - 延期到非涨停日")
    print("   • 一字跌停不卖出 - 延期到非跌停日")
    print("   • 停牌期间不交易 - 等待复牌")
    print("   • 保持信号有效性 - 直到成功执行")
    
    print("\n📊 增强的回测方法已生成")
    enhanced_code = create_enhanced_backtest_method()
    
    print("\n💡 使用方法:")
    print("   1. 将增强交易逻辑集成到策略类中")
    print("   2. 替换原有的backtest_strategy方法")
    print("   3. 运行策略时会自动应用新的交易规则")
    
    print("\n✅ 这将使策略更贴近实际交易情况！")

if __name__ == "__main__":
    main() 