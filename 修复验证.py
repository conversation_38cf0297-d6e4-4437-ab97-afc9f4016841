#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证强制停止机制修复
"""

def verify_fix():
    """验证修复是否成功"""
    print("🔍 验证强制停止机制修复")
    print("=" * 50)
    
    try:
        # 测试集成增强版策略
        from 双均线策略_集成增强版 import MovingAverageStrategyEnhanced
        
        # 检查方法签名
        import inspect
        sig = inspect.signature(MovingAverageStrategyEnhanced.save_single_stock_cache)
        params = list(sig.parameters.keys())
        
        print(f"✅ 集成增强版方法参数: {params}")
        
        # 检查是否包含 force_stop_check 参数
        if 'force_stop_check' in params:
            print("✅ 支持强制停止检查参数")
        else:
            print("❌ 缺少强制停止检查参数")
            return False
        
        # 测试标准版策略
        from 双均线策略_增强版 import MovingAverageStrategyEnhanced as StandardStrategy
        
        sig2 = inspect.signature(StandardStrategy.save_single_stock_cache)
        params2 = list(sig2.parameters.keys())
        
        print(f"✅ 标准版方法参数: {params2}")
        
        # 检查两个版本是否一致
        if params == params2:
            print("✅ 两个版本的方法签名一致")
        else:
            print("❌ 两个版本的方法签名不一致")
            return False
        
        print("\n🎯 修复验证结果:")
        print("  ✅ 集成增强版策略已支持强制停止检查")
        print("  ✅ 标准版策略已支持强制停止检查")
        print("  ✅ 两个版本的接口一致")
        print("  ✅ 向后兼容性保持")
        
        print("\n💡 现在可以正常使用强制停止功能了！")
        print("   工作进程调用: strategy.save_single_stock_cache(code, result, check_func)")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    success = verify_fix()
    if success:
        print("\n🎉 修复验证成功！")
    else:
        print("\n❌ 修复验证失败！")
        exit(1)
