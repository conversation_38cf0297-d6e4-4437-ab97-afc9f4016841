#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
任务管理器 - 持久化任务配置和进度管理
为每个计算任务创建设置文件，支持跨会话的任务恢复
"""

import os
import json
import uuid
import glob
from datetime import datetime
from typing import Dict, List, Optional, Any


class TaskManager:
    """任务管理器类"""
    
    def __init__(self, tasks_dir: str = "tasks"):
        """
        初始化任务管理器
        
        Args:
            tasks_dir: 任务配置文件存储目录
        """
        self.tasks_dir = tasks_dir
        self.ensure_tasks_directory()
        
    def ensure_tasks_directory(self):
        """确保任务目录存在"""
        if not os.path.exists(self.tasks_dir):
            os.makedirs(self.tasks_dir)
            print(f"📁 创建任务目录: {self.tasks_dir}")
    
    def create_task(self, params: Dict[str, Any]) -> str:
        """
        创建新任务并生成配置文件
        
        Args:
            params: 任务参数
            
        Returns:
            str: 任务ID
        """
        # 生成唯一任务ID
        task_id = str(uuid.uuid4())[:8]
        
        # 构建任务配置
        task_config = {
            'task_id': task_id,
            'created_time': datetime.now().isoformat(),
            'updated_time': datetime.now().isoformat(),
            'status': 'created',  # created, running, paused, completed, failed
            'progress': {
                'total_stocks': 0,
                'processed_stocks': 0,
                'successful_stocks': 0,
                'progress_percentage': 0.0
            },
            'parameters': params.copy(),
            'results': {
                'avg_return': 0.0,
                'win_rate': 0.0,
                'max_return': 0.0,
                'min_return': 0.0,
                'total_trades': 0
            },
            'execution_log': [],
            'cache_info': {
                'cache_enabled': params.get('use_cache', True),
                'cached_stocks': 0,
                'cache_directory': ''
            }
        }
        
        # 保存任务配置文件
        self.save_task_config(task_id, task_config)
        
        print(f"📝 创建任务配置: {task_id}")
        return task_id
    
    def save_task_config(self, task_id: str, config: Dict[str, Any]):
        """
        保存任务配置到文件
        
        Args:
            task_id: 任务ID
            config: 任务配置
        """
        try:
            config_file = os.path.join(self.tasks_dir, f"task_{task_id}.json")
            
            # 更新修改时间
            config['updated_time'] = datetime.now().isoformat()
            
            # 使用临时文件安全保存
            temp_file = config_file + '.tmp'
            
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            # 原子性替换
            if os.path.exists(config_file):
                os.remove(config_file)
            os.rename(temp_file, config_file)
            
        except Exception as e:
            print(f"❌ 保存任务配置失败 {task_id}: {str(e)}")
    
    def load_task_config(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        加载任务配置
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict or None: 任务配置
        """
        try:
            config_file = os.path.join(self.tasks_dir, f"task_{task_id}.json")
            
            if not os.path.exists(config_file):
                return None
            
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            print(f"❌ 加载任务配置失败 {task_id}: {str(e)}")
            return None
    
    def update_task_status(self, task_id: str, status: str, **kwargs):
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            **kwargs: 其他要更新的字段
        """
        config = self.load_task_config(task_id)
        if config is None:
            return
        
        config['status'] = status
        config['updated_time'] = datetime.now().isoformat()
        
        # 更新其他字段
        for key, value in kwargs.items():
            if '.' in key:
                # 支持嵌套字段更新，如 'progress.processed_stocks'
                keys = key.split('.')
                target = config
                for k in keys[:-1]:
                    if k not in target:
                        target[k] = {}
                    target = target[k]
                target[keys[-1]] = value
            else:
                config[key] = value
        
        self.save_task_config(task_id, config)
    
    def update_task_progress(self, task_id: str, total_stocks: int = None, 
                           processed_stocks: int = None, successful_stocks: int = None):
        """
        更新任务进度
        
        Args:
            task_id: 任务ID
            total_stocks: 总股票数
            processed_stocks: 已处理股票数
            successful_stocks: 成功处理股票数
        """
        config = self.load_task_config(task_id)
        if config is None:
            return
        
        progress = config.get('progress', {})
        
        if total_stocks is not None:
            progress['total_stocks'] = total_stocks
        if processed_stocks is not None:
            progress['processed_stocks'] = processed_stocks
        if successful_stocks is not None:
            progress['successful_stocks'] = successful_stocks
        
        # 计算进度百分比
        if progress.get('total_stocks', 0) > 0:
            progress['progress_percentage'] = (progress.get('processed_stocks', 0) / 
                                             progress['total_stocks']) * 100
        
        config['progress'] = progress
        self.save_task_config(task_id, config)
    
    def add_execution_log(self, task_id: str, message: str, level: str = 'info'):
        """
        添加执行日志
        
        Args:
            task_id: 任务ID
            message: 日志消息
            level: 日志级别 (info, warning, error)
        """
        config = self.load_task_config(task_id)
        if config is None:
            return
        
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': level,
            'message': message
        }
        
        if 'execution_log' not in config:
            config['execution_log'] = []
        
        config['execution_log'].append(log_entry)
        
        # 限制日志条数，避免文件过大
        if len(config['execution_log']) > 1000:
            config['execution_log'] = config['execution_log'][-500:]  # 保留最新500条
        
        self.save_task_config(task_id, config)
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """
        获取所有任务配置
        
        Returns:
            List[Dict]: 所有任务配置列表
        """
        tasks = []
        
        try:
            task_files = glob.glob(os.path.join(self.tasks_dir, "task_*.json"))
            
            for task_file in task_files:
                try:
                    with open(task_file, 'r', encoding='utf-8') as f:
                        task_config = json.load(f)
                        tasks.append(task_config)
                except Exception as e:
                    print(f"❌ 读取任务文件失败 {task_file}: {str(e)}")
            
            # 按创建时间排序
            tasks.sort(key=lambda x: x.get('created_time', ''), reverse=True)
            
        except Exception as e:
            print(f"❌ 获取任务列表失败: {str(e)}")
        
        return tasks
    
    def get_tasks_by_status(self, status: str) -> List[Dict[str, Any]]:
        """
        根据状态获取任务
        
        Args:
            status: 任务状态
            
        Returns:
            List[Dict]: 指定状态的任务列表
        """
        all_tasks = self.get_all_tasks()
        return [task for task in all_tasks if task.get('status') == status]
    
    def delete_task(self, task_id: str) -> bool:
        """
        删除任务配置文件
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            config_file = os.path.join(self.tasks_dir, f"task_{task_id}.json")
            
            if os.path.exists(config_file):
                os.remove(config_file)
                print(f"🗑️ 删除任务配置: {task_id}")
                return True
            else:
                print(f"⚠️ 任务配置文件不存在: {task_id}")
                return False
                
        except Exception as e:
            print(f"❌ 删除任务配置失败 {task_id}: {str(e)}")
            return False
    
    def cleanup_old_tasks(self, days: int = 30):
        """
        清理旧任务配置文件
        
        Args:
            days: 保留天数
        """
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days)
            
            all_tasks = self.get_all_tasks()
            cleaned_count = 0
            
            for task in all_tasks:
                try:
                    created_time = datetime.fromisoformat(task.get('created_time', ''))
                    if created_time < cutoff_date and task.get('status') in ['completed', 'failed']:
                        if self.delete_task(task['task_id']):
                            cleaned_count += 1
                except:
                    continue
            
            print(f"🧹 清理了 {cleaned_count} 个旧任务配置")
            
        except Exception as e:
            print(f"❌ 清理旧任务失败: {str(e)}")


# 全局任务管理器实例
task_manager = TaskManager()
