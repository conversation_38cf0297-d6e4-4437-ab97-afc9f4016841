{"task_id": "fd73acd6", "created_time": "2025-05-27T21:39:36.427065", "updated_time": "2025-05-27T21:39:46.201549", "status": "running", "progress": {"total_stocks": 5435, "processed_stocks": 308, "successful_stocks": 0, "progress_percentage": 5.666973321067157}, "parameters": {"type": "preset", "name": "激进策略", "short_window": 3, "long_window": 10, "data_dir": "../日线数据", "execution_price": "open", "use_enhanced_logic": true, "use_cache": true, "generate_charts": false, "save_interval": 20}, "results": {"avg_return": 0.0, "win_rate": 0.0, "max_return": 0.0, "min_return": 0.0, "total_trades": 0}, "execution_log": [{"timestamp": "2025-05-27T21:39:38.072618", "level": "info", "message": "任务开始执行，参数: {'type': 'preset', 'name': '激进策略', 'short_window': 3, 'long_window': 10, 'data_dir': '../日线数据', 'execution_price': 'open', 'use_enhanced_logic': True, 'use_cache': True, 'generate_charts': False, 'save_interval': 20, 'task_id': 'fd73acd6'}"}, {"timestamp": "2025-05-27T21:39:46.201530", "level": "info", "message": "开始处理 5127 只股票，已缓存 308 只"}], "cache_info": {"cache_enabled": true, "cached_stocks": 0, "cache_directory": ""}}