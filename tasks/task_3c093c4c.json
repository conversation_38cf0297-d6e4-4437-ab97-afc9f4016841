{"task_id": "3c093c4c", "created_time": "2025-05-24T21:46:32.780115", "updated_time": "2025-05-27T21:52:49.616293", "status": "running", "progress": {"total_stocks": 5435, "processed_stocks": 80, "successful_stocks": 80, "progress_percentage": 1.4719411223551058}, "parameters": {"name": "保守策略", "type": "preset", "short_window": 10, "long_window": 30, "data_dir": "../日线数据", "execution_price": "random", "use_enhanced_logic": true, "use_cache": true, "generate_charts": false, "save_interval": 20}, "results": {"avg_return": 0.05, "win_rate": 0.55, "max_return": 0.28, "min_return": -0.15, "total_trades": 0}, "execution_log": [{"timestamp": "2025-05-27T21:46:32.805207", "level": "info", "message": "任务创建: 保守策略"}, {"timestamp": "2025-05-27T21:46:32.811745", "level": "error", "message": "任务执行失败"}, {"timestamp": "2025-05-27T21:47:15.662354", "level": "info", "message": "任务开始执行，参数: {'name': '保守策略', 'type': 'preset', 'short_window': 10, 'long_window': 30, 'data_dir': '../日线数据', 'execution_price': 'random', 'use_enhanced_logic': True, 'use_cache': True, 'generate_charts': False, 'save_interval': 20, 'task_id': '3c093c4c'}"}, {"timestamp": "2025-05-27T21:47:16.741507", "level": "info", "message": "开始处理 5435 只股票，已缓存 0 只"}, {"timestamp": "2025-05-27T21:48:36.508816", "level": "info", "message": "进度更新: 20/5435 (0.4%)"}, {"timestamp": "2025-05-27T21:49:52.367457", "level": "info", "message": "进度更新: 40/5435 (0.7%)"}, {"timestamp": "2025-05-27T21:51:23.590292", "level": "info", "message": "进度更新: 60/5435 (1.1%)"}, {"timestamp": "2025-05-27T21:52:49.616283", "level": "info", "message": "进度更新: 80/5435 (1.5%)"}], "cache_info": {"cache_enabled": true, "cached_stocks": 0, "cache_directory": ""}}