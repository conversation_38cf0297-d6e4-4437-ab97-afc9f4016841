{"task_id": "93ad26c4", "created_time": "2025-05-27T21:38:12.388477", "updated_time": "2025-05-27T21:38:12.470726", "status": "completed", "progress": {"total_stocks": 1000, "processed_stocks": 1000, "successful_stocks": 850, "progress_percentage": 100.0}, "parameters": {"type": "preset", "name": "稳健策略", "short_window": 5, "long_window": 20, "data_dir": "../日线数据", "execution_price": "open", "use_enhanced_logic": true, "use_cache": true, "generate_charts": false, "save_interval": 20}, "results": {"avg_return": 0.18, "win_rate": 0.68, "max_return": 0.52, "min_return": -0.18, "total_trades": 850}, "execution_log": [{"timestamp": "2025-05-27T21:38:12.413671", "level": "info", "message": "开始处理股票数据"}, {"timestamp": "2025-05-27T21:38:12.421985", "level": "info", "message": "处理了250只股票"}, {"timestamp": "2025-05-27T21:38:12.430307", "level": "warning", "message": "发现一些数据异常"}, {"timestamp": "2025-05-27T21:38:12.470709", "level": "info", "message": "任务执行完成，处理了1000只股票"}], "cache_info": {"cache_enabled": true, "cached_stocks": 0, "cache_directory": ""}}