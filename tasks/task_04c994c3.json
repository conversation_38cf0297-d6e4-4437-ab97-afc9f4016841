{"task_id": "04c994c3", "created_time": "2025-05-25T21:46:32.747777", "updated_time": "2025-05-27T21:51:26.358514", "status": "running", "progress": {"total_stocks": 5435, "processed_stocks": 60, "successful_stocks": 60, "progress_percentage": 1.1039558417663293}, "parameters": {"name": "稳健策略", "type": "preset", "short_window": 5, "long_window": 20, "data_dir": "../日线数据", "execution_price": "close", "use_enhanced_logic": true, "use_cache": true, "generate_charts": false, "save_interval": 20}, "results": {"avg_return": 0.08, "win_rate": 0.58, "max_return": 0.35, "min_return": -0.18, "total_trades": 0}, "execution_log": [{"timestamp": "2025-05-27T21:46:32.771619", "level": "info", "message": "任务创建: 稳健策略"}, {"timestamp": "2025-05-27T21:46:32.778552", "level": "warning", "message": "任务被用户暂停"}, {"timestamp": "2025-05-27T21:47:15.644446", "level": "info", "message": "任务开始执行，参数: {'name': '稳健策略', 'type': 'preset', 'short_window': 5, 'long_window': 20, 'data_dir': '../日线数据', 'execution_price': 'close', 'use_enhanced_logic': True, 'use_cache': True, 'generate_charts': False, 'save_interval': 20, 'task_id': '04c994c3'}"}, {"timestamp": "2025-05-27T21:47:16.717185", "level": "info", "message": "开始处理 5435 只股票，已缓存 0 只"}, {"timestamp": "2025-05-27T21:48:37.212833", "level": "info", "message": "进度更新: 20/5435 (0.4%)"}, {"timestamp": "2025-05-27T21:49:54.012697", "level": "info", "message": "进度更新: 40/5435 (0.7%)"}, {"timestamp": "2025-05-27T21:51:26.358502", "level": "info", "message": "进度更新: 60/5435 (1.1%)"}], "cache_info": {"cache_enabled": true, "cached_stocks": 0, "cache_directory": ""}}