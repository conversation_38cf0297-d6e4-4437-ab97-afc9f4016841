{"task_id": "36b3ff02", "created_time": "2025-05-23T21:46:32.813362", "updated_time": "2025-05-27T21:51:59.847204", "status": "running", "progress": {"total_stocks": 5435, "processed_stocks": 50, "successful_stocks": 50, "progress_percentage": 0.9199632014719411}, "parameters": {"name": "自定义策略", "type": "custom", "short_window": 7, "long_window": 25, "data_dir": "../日线数据", "execution_price": "open", "use_enhanced_logic": false, "use_cache": true, "generate_charts": true, "save_interval": 50}, "results": {"avg_return": 0.0, "win_rate": 0.0, "max_return": 0.0, "min_return": 0.0, "total_trades": 0}, "execution_log": [{"timestamp": "2025-05-27T21:46:32.836240", "level": "info", "message": "任务创建: 自定义策略"}, {"timestamp": "2025-05-27T21:47:15.705294", "level": "info", "message": "任务开始执行，参数: {'name': '自定义策略', 'type': 'custom', 'short_window': 7, 'long_window': 25, 'data_dir': '../日线数据', 'execution_price': 'open', 'use_enhanced_logic': False, 'use_cache': True, 'generate_charts': True, 'save_interval': 50, 'task_id': '36b3ff02'}"}, {"timestamp": "2025-05-27T21:47:16.771989", "level": "info", "message": "开始处理 5435 只股票，已缓存 0 只"}, {"timestamp": "2025-05-27T21:51:59.847195", "level": "info", "message": "进度更新: 50/5435 (0.9%)"}], "cache_info": {"cache_enabled": true, "cached_stocks": 0, "cache_directory": ""}}