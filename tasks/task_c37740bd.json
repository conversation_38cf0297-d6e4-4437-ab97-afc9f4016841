{"task_id": "c37740bd", "created_time": "2025-05-26T21:46:32.711568", "updated_time": "2025-05-27T21:51:40.559616", "status": "running", "progress": {"total_stocks": 5435, "processed_stocks": 373, "successful_stocks": 373, "progress_percentage": 6.862925482980681}, "parameters": {"name": "激进策略", "type": "preset", "short_window": 3, "long_window": 10, "data_dir": "../日线数据", "execution_price": "open", "use_enhanced_logic": true, "use_cache": true, "generate_charts": false, "save_interval": 20}, "results": {"avg_return": 0.12, "win_rate": 0.62, "max_return": 0.45, "min_return": -0.25, "total_trades": 0}, "execution_log": [{"timestamp": "2025-05-27T21:46:32.740257", "level": "info", "message": "任务创建: 激进策略"}, {"timestamp": "2025-05-27T21:46:32.746230", "level": "info", "message": "任务成功完成"}, {"timestamp": "2025-05-27T21:47:15.596783", "level": "info", "message": "任务开始执行，参数: {'name': '激进策略', 'type': 'preset', 'short_window': 3, 'long_window': 10, 'data_dir': '../日线数据', 'execution_price': 'open', 'use_enhanced_logic': True, 'use_cache': True, 'generate_charts': False, 'save_interval': 20, 'task_id': 'c37740bd'}"}, {"timestamp": "2025-05-27T21:47:23.772279", "level": "info", "message": "开始处理 5122 只股票，已缓存 313 只"}, {"timestamp": "2025-05-27T21:48:51.352241", "level": "info", "message": "进度更新: 333/5435 (6.1%)"}, {"timestamp": "2025-05-27T21:50:19.029585", "level": "info", "message": "进度更新: 353/5435 (6.5%)"}, {"timestamp": "2025-05-27T21:51:40.559606", "level": "info", "message": "进度更新: 373/5435 (6.9%)"}], "cache_info": {"cache_enabled": true, "cached_stocks": 0, "cache_directory": ""}}