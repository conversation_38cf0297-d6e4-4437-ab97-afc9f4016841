#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的缓存文件检查工具 - 命令行版本
"""

import os
import glob
import pickle

def check_cache_files():
    """检查缓存文件的完整性"""
    cache_dir = "./cache"
    
    if not os.path.exists(cache_dir):
        print("❌ 缓存目录不存在")
        return
    
    cache_files = glob.glob(os.path.join(cache_dir, "*.pkl"))
    print(f"📁 找到 {len(cache_files)} 个缓存文件")
    
    if not cache_files:
        print("📝 缓存目录为空")
        return
    
    valid_files = []
    corrupted_files = []
    empty_files = []
    
    for pkl_file in cache_files:
        filename = os.path.basename(pkl_file)
        file_size = os.path.getsize(pkl_file)
        
        print(f"\n📄 检查文件: {filename}")
        print(f"   文件大小: {file_size:,} 字节")
        
        if file_size == 0:
            print(f"   ❌ 文件为空")
            empty_files.append(pkl_file)
            continue
        
        try:
            with open(pkl_file, 'rb') as f:
                cache_data = pickle.load(f)
            
            # 验证数据结构
            if isinstance(cache_data, dict):
                stock_count = len(cache_data)
                print(f"   ✅ 文件正常，包含 {stock_count} 只股票数据")
                valid_files.append(pkl_file)
            else:
                print(f"   ⚠️ 数据格式异常: {type(cache_data)}")
                corrupted_files.append(pkl_file)
                
        except EOFError:
            print(f"   ❌ 文件损坏: EOFError (文件不完整)")
            corrupted_files.append(pkl_file)
        except pickle.UnpicklingError as e:
            print(f"   ❌ 文件损坏: UnpicklingError - {str(e)}")
            corrupted_files.append(pkl_file)
        except Exception as e:
            print(f"   ❌ 读取失败: {str(e)}")
            corrupted_files.append(pkl_file)
    
    # 汇总结果
    print(f"\n" + "="*50)
    print(f"📊 扫描结果汇总:")
    print(f"   ✅ 正常文件: {len(valid_files)} 个")
    print(f"   ❌ 损坏文件: {len(corrupted_files)} 个")
    print(f"   📝 空文件: {len(empty_files)} 个")
    
    if corrupted_files:
        print(f"\n🚨 损坏的文件列表:")
        for f in corrupted_files:
            print(f"   - {os.path.basename(f)}")
    
    if empty_files:
        print(f"\n📝 空文件列表:")
        for f in empty_files:
            print(f"   - {os.path.basename(f)}")
    
    # 询问是否删除损坏文件
    all_bad_files = corrupted_files + empty_files
    if all_bad_files:
        print(f"\n❓ 是否删除这 {len(all_bad_files)} 个损坏/空文件？")
        print("   删除后这些策略需要重新计算")
        choice = input("   输入 'y' 确认删除，其他键取消: ").lower().strip()
        
        if choice == 'y':
            deleted_count = 0
            for pkl_file in all_bad_files:
                try:
                    os.remove(pkl_file)
                    print(f"   ✅ 已删除: {os.path.basename(pkl_file)}")
                    deleted_count += 1
                except Exception as e:
                    print(f"   ❌ 删除失败: {os.path.basename(pkl_file)} - {str(e)}")
            
            print(f"\n✅ 删除完成，共删除 {deleted_count} 个文件")
        else:
            print("\n📝 已取消删除操作")
    else:
        print(f"\n🎉 所有缓存文件都正常！")

if __name__ == "__main__":
    print("缓存文件检查工具")
    print("=" * 30)
    check_cache_files()
    print("\n检查完成！")
