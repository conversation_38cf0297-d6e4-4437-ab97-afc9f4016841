#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缓存修复工具 - 检测和修复损坏的pickle缓存文件
"""

import os
import glob
import pickle
import shutil
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext

class CacheRepairTool:
    def __init__(self, root):
        self.root = root
        self.root.title("缓存修复工具")
        self.root.geometry("800x600")
        
        self.cache_dir = "./cache"
        self.backup_dir = "./cache_backup"
        
        self.create_widgets()
        self.scan_cache_files()
    
    def create_widgets(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="策略缓存文件修复工具", 
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # 控制按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(button_frame, text="🔍 扫描缓存文件", 
                  command=self.scan_cache_files).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="🗑️ 删除损坏文件", 
                  command=self.delete_corrupted_files).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="💾 备份所有缓存", 
                  command=self.backup_cache_files).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="🔄 恢复备份", 
                  command=self.restore_backup).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="🧹 清空所有缓存", 
                  command=self.clear_all_cache).pack(side=tk.LEFT)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="扫描结果", padding="10")
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def scan_cache_files(self):
        """扫描缓存文件"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("🔍 开始扫描缓存文件...")
        
        if not os.path.exists(self.cache_dir):
            self.log_message("❌ 缓存目录不存在")
            return
        
        cache_files = glob.glob(os.path.join(self.cache_dir, "*.pkl"))
        self.log_message(f"📁 找到 {len(cache_files)} 个缓存文件")
        
        if not cache_files:
            self.log_message("📝 缓存目录为空")
            return
        
        valid_files = []
        corrupted_files = []
        empty_files = []
        
        for pkl_file in cache_files:
            filename = os.path.basename(pkl_file)
            file_size = os.path.getsize(pkl_file)
            
            self.log_message(f"\n📄 检查文件: {filename}")
            self.log_message(f"   文件大小: {file_size:,} 字节")
            
            if file_size == 0:
                self.log_message(f"   ❌ 文件为空")
                empty_files.append(pkl_file)
                continue
            
            try:
                with open(pkl_file, 'rb') as f:
                    cache_data = pickle.load(f)
                
                # 验证数据结构
                if isinstance(cache_data, dict):
                    stock_count = len(cache_data)
                    self.log_message(f"   ✅ 文件正常，包含 {stock_count} 只股票数据")
                    valid_files.append(pkl_file)
                else:
                    self.log_message(f"   ⚠️ 数据格式异常: {type(cache_data)}")
                    corrupted_files.append(pkl_file)
                    
            except EOFError:
                self.log_message(f"   ❌ 文件损坏: EOFError (文件不完整)")
                corrupted_files.append(pkl_file)
            except pickle.UnpicklingError as e:
                self.log_message(f"   ❌ 文件损坏: UnpicklingError - {str(e)}")
                corrupted_files.append(pkl_file)
            except Exception as e:
                self.log_message(f"   ❌ 读取失败: {str(e)}")
                corrupted_files.append(pkl_file)
        
        # 汇总结果
        self.log_message(f"\n" + "="*50)
        self.log_message(f"📊 扫描结果汇总:")
        self.log_message(f"   ✅ 正常文件: {len(valid_files)} 个")
        self.log_message(f"   ❌ 损坏文件: {len(corrupted_files)} 个")
        self.log_message(f"   📝 空文件: {len(empty_files)} 个")
        
        if corrupted_files:
            self.log_message(f"\n🚨 损坏的文件列表:")
            for f in corrupted_files:
                self.log_message(f"   - {os.path.basename(f)}")
        
        if empty_files:
            self.log_message(f"\n📝 空文件列表:")
            for f in empty_files:
                self.log_message(f"   - {os.path.basename(f)}")
        
        self.corrupted_files = corrupted_files + empty_files
        self.status_var.set(f"扫描完成: {len(valid_files)}正常, {len(self.corrupted_files)}损坏")
    
    def delete_corrupted_files(self):
        """删除损坏的文件"""
        if not hasattr(self, 'corrupted_files') or not self.corrupted_files:
            messagebox.showinfo("提示", "没有发现损坏的文件")
            return
        
        confirm = messagebox.askyesno("确认删除", 
                                     f"确定要删除 {len(self.corrupted_files)} 个损坏的缓存文件吗？\n"
                                     "删除后这些策略需要重新计算。")
        if not confirm:
            return
        
        self.log_message(f"\n🗑️ 开始删除损坏文件...")
        deleted_count = 0
        
        for pkl_file in self.corrupted_files:
            try:
                os.remove(pkl_file)
                self.log_message(f"   ✅ 已删除: {os.path.basename(pkl_file)}")
                deleted_count += 1
            except Exception as e:
                self.log_message(f"   ❌ 删除失败: {os.path.basename(pkl_file)} - {str(e)}")
        
        self.log_message(f"\n✅ 删除完成，共删除 {deleted_count} 个文件")
        self.status_var.set(f"已删除 {deleted_count} 个损坏文件")
        
        # 重新扫描
        self.scan_cache_files()
    
    def backup_cache_files(self):
        """备份所有缓存文件"""
        if not os.path.exists(self.cache_dir):
            messagebox.showerror("错误", "缓存目录不存在")
            return
        
        cache_files = glob.glob(os.path.join(self.cache_dir, "*.pkl"))
        if not cache_files:
            messagebox.showinfo("提示", "没有缓存文件需要备份")
            return
        
        # 创建备份目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{self.backup_dir}_{timestamp}"
        os.makedirs(backup_path, exist_ok=True)
        
        self.log_message(f"\n💾 开始备份缓存文件到: {backup_path}")
        
        backup_count = 0
        for pkl_file in cache_files:
            try:
                filename = os.path.basename(pkl_file)
                backup_file = os.path.join(backup_path, filename)
                shutil.copy2(pkl_file, backup_file)
                self.log_message(f"   ✅ 已备份: {filename}")
                backup_count += 1
            except Exception as e:
                self.log_message(f"   ❌ 备份失败: {filename} - {str(e)}")
        
        self.log_message(f"\n✅ 备份完成，共备份 {backup_count} 个文件")
        self.status_var.set(f"已备份 {backup_count} 个文件到 {backup_path}")
    
    def restore_backup(self):
        """恢复备份"""
        # 查找最新的备份目录
        backup_dirs = glob.glob(f"{self.backup_dir}_*")
        if not backup_dirs:
            messagebox.showinfo("提示", "没有找到备份文件")
            return
        
        latest_backup = max(backup_dirs, key=os.path.getctime)
        
        confirm = messagebox.askyesno("确认恢复", 
                                     f"确定要从以下备份恢复缓存文件吗？\n{latest_backup}\n"
                                     "这将覆盖当前的缓存文件。")
        if not confirm:
            return
        
        backup_files = glob.glob(os.path.join(latest_backup, "*.pkl"))
        if not backup_files:
            messagebox.showerror("错误", "备份目录中没有找到缓存文件")
            return
        
        self.log_message(f"\n🔄 开始从备份恢复: {latest_backup}")
        
        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)
        
        restore_count = 0
        for backup_file in backup_files:
            try:
                filename = os.path.basename(backup_file)
                cache_file = os.path.join(self.cache_dir, filename)
                shutil.copy2(backup_file, cache_file)
                self.log_message(f"   ✅ 已恢复: {filename}")
                restore_count += 1
            except Exception as e:
                self.log_message(f"   ❌ 恢复失败: {filename} - {str(e)}")
        
        self.log_message(f"\n✅ 恢复完成，共恢复 {restore_count} 个文件")
        self.status_var.set(f"已恢复 {restore_count} 个文件")
        
        # 重新扫描
        self.scan_cache_files()
    
    def clear_all_cache(self):
        """清空所有缓存"""
        if not os.path.exists(self.cache_dir):
            messagebox.showinfo("提示", "缓存目录不存在")
            return
        
        cache_files = glob.glob(os.path.join(self.cache_dir, "*.pkl"))
        if not cache_files:
            messagebox.showinfo("提示", "缓存目录已经为空")
            return
        
        confirm = messagebox.askyesno("确认清空", 
                                     f"确定要删除所有 {len(cache_files)} 个缓存文件吗？\n"
                                     "删除后所有策略都需要重新计算。\n"
                                     "建议先进行备份。")
        if not confirm:
            return
        
        self.log_message(f"\n🧹 开始清空所有缓存文件...")
        
        deleted_count = 0
        for pkl_file in cache_files:
            try:
                os.remove(pkl_file)
                self.log_message(f"   ✅ 已删除: {os.path.basename(pkl_file)}")
                deleted_count += 1
            except Exception as e:
                self.log_message(f"   ❌ 删除失败: {os.path.basename(pkl_file)} - {str(e)}")
        
        self.log_message(f"\n✅ 清空完成，共删除 {deleted_count} 个文件")
        self.status_var.set(f"已清空所有缓存，删除 {deleted_count} 个文件")
        
        # 重新扫描
        self.scan_cache_files()

def main():
    root = tk.Tk()
    app = CacheRepairTool(root)
    root.mainloop()

if __name__ == "__main__":
    main()
