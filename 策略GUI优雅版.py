#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
双均线策略图形界面 - 支持优雅停止版本
不使用Event对象，通过文件标志实现优雅停止功能
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
import queue
import os
import glob
import time
from datetime import datetime
import sys
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import uuid
import signal

# 修复matplotlib字体设置问题
try:
    import matplotlib
    # 设置后端
    if sys.platform.startswith('win'):
        matplotlib.use('TkAgg')
    else:
        matplotlib.use('Qt5Agg')

    import matplotlib.pyplot as plt
    import matplotlib.font_manager as fm

    # 强制设置中文字体 - 避免字体问题
    chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'sans-serif']
    plt.rcParams['font.sans-serif'] = chinese_fonts
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['axes.unicode_minus'] = False

    # 设置图表基本参数
    plt.rcParams['figure.facecolor'] = 'white'
    plt.rcParams['axes.facecolor'] = 'white'
    plt.rcParams['savefig.facecolor'] = 'white'

    print("✅ matplotlib字体设置完成")
except Exception as e:
    print(f"⚠️ matplotlib设置警告: {str(e)}")

# 导入策略模块
from 双均线策略_增强版 import MovingAverageStrategyEnhanced
from 策略配置 import get_strategy_config, PRESET_STRATEGIES
from 策略工作进程_优雅版 import strategy_worker_process_graceful

class GracefulStrategyGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("双均线策略系统 - 优雅停止版")
        self.root.geometry("1400x900")

        # 设置窗口图标和样式
        self.setup_style()

        # 并行任务管理
        self.thread_queue = queue.Queue()
        self.running_tasks = {}  # task_id -> task_info
        self.executor = ProcessPoolExecutor(max_workers=mp.cpu_count())
        self.futures = {}  # future -> task_id
        self.graceful_stop_timeout = 30  # 优雅停止超时时间（秒）

        # 策略实例
        self.current_strategy = None

        # 添加窗口状态标志和after调度管理
        self.window_active = True
        self.after_ids = []  # 保存所有after调度的ID

        # 创建界面
        self.create_widgets()

        # 启动队列监控
        self.check_queue()

        # 启动任务监控
        self.monitor_tasks()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_closing)

    def setup_style(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')

        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Header.TLabel', font=('Arial', 10, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Running.TLabel', foreground='blue')
        style.configure('Stopping.TLabel', foreground='orange')

    def create_widgets(self):
        """创建主界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

        # 创建各个功能区域
        self.create_strategy_selection(main_frame)
        self.create_control_panel(main_frame)
        self.create_task_status_panel(main_frame)
        self.create_results_panel(main_frame)
        self.create_log_panel(main_frame)

    def create_strategy_selection(self, parent):
        """创建策略选择区域"""
        # 策略选择框架
        strategy_frame = ttk.LabelFrame(parent, text="策略参数配置", padding="10")
        strategy_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        strategy_frame.columnconfigure(1, weight=1)

        # 策略类型选择
        ttk.Label(strategy_frame, text="策略类型:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.strategy_type = tk.StringVar(value="preset")
        ttk.Radiobutton(strategy_frame, text="预设策略", variable=self.strategy_type,
                       value="preset", command=self.on_strategy_type_change).grid(row=0, column=1, sticky=tk.W)
        ttk.Radiobutton(strategy_frame, text="自定义参数", variable=self.strategy_type,
                       value="custom", command=self.on_strategy_type_change).grid(row=0, column=2, sticky=tk.W)

        # 预设策略选择
        ttk.Label(strategy_frame, text="预设策略:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))

        self.preset_strategy = tk.StringVar()
        self.preset_combo = ttk.Combobox(strategy_frame, textvariable=self.preset_strategy,
                                        values=list(PRESET_STRATEGIES.keys()), state="readonly")
        self.preset_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(10, 0), padx=(0, 10))
        if PRESET_STRATEGIES:
            self.preset_combo.set(list(PRESET_STRATEGIES.keys())[0])

        # 自定义参数
        custom_frame = ttk.Frame(strategy_frame)
        custom_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(custom_frame, text="短期均线:").grid(row=0, column=0, sticky=tk.W)
        self.short_window = tk.StringVar(value="5")
        ttk.Entry(custom_frame, textvariable=self.short_window, width=8).grid(row=0, column=1, padx=(5, 20))

        ttk.Label(custom_frame, text="长期均线:").grid(row=0, column=2, sticky=tk.W)
        self.long_window = tk.StringVar(value="20")
        ttk.Entry(custom_frame, textvariable=self.long_window, width=8).grid(row=0, column=3, padx=(5, 20))

        # 执行价格类型选择
        execution_frame = ttk.Frame(strategy_frame)
        execution_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(execution_frame, text="执行价格:").grid(row=0, column=0, sticky=tk.W)
        self.execution_price = tk.StringVar(value="open")

        from 策略配置 import EXECUTION_PRICE_TYPES
        execution_options = []
        for key, info in EXECUTION_PRICE_TYPES.items():
            execution_options.append((key, info['name']))

        for i, (value, text) in enumerate(execution_options):
            ttk.Radiobutton(execution_frame, text=text, variable=self.execution_price,
                           value=value).grid(row=0, column=i+1, sticky=tk.W, padx=(10, 0))

        # 增强功能选择
        enhanced_frame = ttk.Frame(strategy_frame)
        enhanced_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        self.use_enhanced_logic = tk.BooleanVar(value=True)
        ttk.Checkbutton(enhanced_frame, text="启用增强交易逻辑（停牌和涨跌停检测）",
                       variable=self.use_enhanced_logic).grid(row=0, column=0, sticky=tk.W)

        # 运行选项
        options_frame = ttk.Frame(strategy_frame)
        options_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        self.use_cache = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="使用缓存", variable=self.use_cache).grid(row=0, column=0, sticky=tk.W)

        self.generate_charts = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text="生成图表", variable=self.generate_charts).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))

        ttk.Label(options_frame, text="进度显示间隔:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5))
        self.save_interval = tk.StringVar(value="20")
        ttk.Entry(options_frame, textvariable=self.save_interval, width=8).grid(row=0, column=3, padx=(0, 5))
        ttk.Label(options_frame, text="只股票").grid(row=0, column=4, sticky=tk.W)

        # 初始状态设置
        self.on_strategy_type_change()

    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="并行运行控制 - 支持优雅停止", padding="10")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 第一行按钮
        button_frame1 = ttk.Frame(control_frame)
        button_frame1.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame1, text="🚀 添加到队列",
                  command=self.add_task_to_queue).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame1, text="🔄 批量运行预设策略",
                  command=self.run_all_presets).pack(side=tk.LEFT, padx=(0, 10))

        # 优雅停止按钮（突出显示）
        ttk.Button(button_frame1, text="⏸️ 优雅停止（等待完成）",
                  command=self.graceful_stop_all_tasks).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame1, text="🛑 强制停止（立即停止）",
                  command=self.force_stop_all_tasks).pack(side=tk.LEFT, padx=(0, 10))

        # 第二行按钮
        button_frame2 = ttk.Frame(control_frame)
        button_frame2.pack(fill=tk.X)

        ttk.Button(button_frame2, text="💾 缓存管理",
                  command=self.open_cache_manager).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame2, text="📈 多策略对比",
                  command=self.open_multi_strategy_comparison).pack(side=tk.LEFT, padx=(0, 10))

        # 状态显示
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(status_frame, text=f"💻 CPU核心数: {mp.cpu_count()}").pack(side=tk.LEFT)
        self.cpu_usage_label = ttk.Label(status_frame, text="活跃任务: 0")
        self.cpu_usage_label.pack(side=tk.LEFT, padx=(20, 0))

        # 优雅停止超时设置
        timeout_frame = ttk.Frame(status_frame)
        timeout_frame.pack(side=tk.RIGHT)
        ttk.Label(timeout_frame, text="优雅停止超时:").pack(side=tk.LEFT)
        self.timeout_var = tk.StringVar(value=str(self.graceful_stop_timeout))
        ttk.Entry(timeout_frame, textvariable=self.timeout_var, width=5).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(timeout_frame, text="秒").pack(side=tk.LEFT)

    def create_task_status_panel(self, parent):
        """创建任务状态面板"""
        status_frame = ttk.LabelFrame(parent, text="任务状态监控", padding="10")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(0, weight=1)

        # 任务列表
        columns = ('任务ID', '策略参数', '状态', '开始时间', '耗时', '进度')
        self.task_tree = ttk.Treeview(status_frame, columns=columns, show='headings', height=6)

        for col in columns:
            self.task_tree.heading(col, text=col)
            self.task_tree.column(col, width=100, anchor='center')

        # 调整列宽
        self.task_tree.column('任务ID', width=80)
        self.task_tree.column('策略参数', width=120)
        self.task_tree.column('状态', width=100)
        self.task_tree.column('开始时间', width=150)

        # 滚动条
        task_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.task_tree.yview)
        self.task_tree.configure(yscrollcommand=task_scrollbar.set)

        self.task_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        task_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 右键菜单
        self.create_context_menu()

    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="⏸️ 优雅停止该任务（等待完成）", command=self.graceful_stop_selected_task)
        self.context_menu.add_command(label="🛑 强制停止该任务（立即停止）", command=self.force_stop_selected_task)

        self.task_tree.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """显示右键菜单"""
        selection = self.task_tree.selection()
        if selection:
            self.context_menu.post(event.x_root, event.y_root)

    def graceful_stop_selected_task(self):
        """优雅停止选中的任务"""
        selection = self.task_tree.selection()
        if not selection:
            return

        item = selection[0]
        task_id = self.task_tree.item(item)['values'][0]

        if task_id in self.running_tasks:
            self.graceful_stop_task(task_id)

    def force_stop_selected_task(self):
        """强制停止选中的任务"""
        selection = self.task_tree.selection()
        if not selection:
            return

        item = selection[0]
        task_id = self.task_tree.item(item)['values'][0]

        if task_id in self.running_tasks:
            self.force_stop_task(task_id)

    def create_results_panel(self, parent):
        """创建结果显示面板"""
        results_frame = ttk.LabelFrame(parent, text="运行结果", padding="10")
        results_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # 创建Treeview显示结果
        columns = ('策略', '股票数', '平均收益率', '胜率', '最大收益', '最小收益', '完成时间', '状态')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=10)

        # 设置列标题
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=100, anchor='center')

        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        scrollbar_h = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

        self.results_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))

    def create_log_panel(self, parent):
        """创建日志输出面板"""
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 创建滚动文本区域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 按钮框架
        button_frame = ttk.Frame(log_frame)
        button_frame.grid(row=1, column=0, pady=(5, 0), sticky=tk.W)

        ttk.Button(button_frame, text="清除日志",
                  command=lambda: self.log_text.delete(1.0, tk.END)).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="保存日志",
                  command=self.save_log).pack(side=tk.LEFT)

    def save_log(self):
        """保存日志到文件"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.log_message(f"📁 日志已保存到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {str(e)}")

    def on_strategy_type_change(self):
        """策略类型改变时的处理"""
        if self.strategy_type.get() == "preset":
            self.preset_combo.configure(state="readonly")
        else:
            self.preset_combo.configure(state="disabled")

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def add_task_to_queue(self):
        """添加任务到执行队列"""
        # 验证参数
        if not self.validate_parameters():
            return

        # 获取参数
        params = self.get_strategy_parameters()

        # 生成唯一任务ID
        task_id = str(uuid.uuid4())[:8]
        params['task_id'] = task_id

        # 提交任务到进程池
        future = self.executor.submit(strategy_worker_process_graceful, params)
        self.futures[future] = task_id

        # 记录任务信息
        # 添加执行价格信息到策略名称
        execution_suffix = {
            'open': '_open',
            'close': '_close',
            'random': '_rand'
        }.get(params.get('execution_price', 'open'), '_open')

        base_name = params.get('name', f"MA{params['short_window']}-MA{params['long_window']}")
        strategy_name = base_name + execution_suffix

        self.running_tasks[task_id] = {
            'params': params,
            'future': future,
            'start_time': datetime.now(),
            'status': '运行中',
            'strategy_name': strategy_name
        }

        # 更新任务状态表
        self.update_task_status(task_id, strategy_name, '🔄 运行中', datetime.now().strftime("%H:%M:%S"))

        self.log_message(f"✅ 已添加任务: {strategy_name} (ID: {task_id})")
        self.log_message(f"📊 当前并行任务数: {len(self.running_tasks)}")

    def run_all_presets(self):
        """批量运行所有预设策略 - 包含所有执行价格类型"""
        if not PRESET_STRATEGIES:
            messagebox.showwarning("提示", "没有可用的预设策略")
            return

        # 执行价格类型列表
        execution_types = ['open', 'close', 'random']
        total_strategies = len(PRESET_STRATEGIES) * len(execution_types)

        confirm = messagebox.askyesno("确认批量运行",
                                     f"将同时运行 {len(PRESET_STRATEGIES)} 个预设策略的 {len(execution_types)} 种执行价格变种，\n"
                                     f"总计 {total_strategies} 个策略组合。\n"
                                     f"这将充分利用您的 {mp.cpu_count()} 核CPU。\n"
                                     "确认继续吗？")
        if not confirm:
            return

        self.log_message(f"🚀 开始批量运行 {total_strategies} 个策略组合...")
        self.log_message(f"📋 策略: {list(PRESET_STRATEGIES.keys())}")
        self.log_message(f"📋 执行价格: {execution_types}")

        for strategy_name in PRESET_STRATEGIES.keys():
            config = get_strategy_config(strategy_name)

            for execution_price in execution_types:
                # 获取执行价格的中文名称
                execution_price_name = {
                    'open': '开盘价',
                    'close': '收盘价',
                    'random': '随机价'
                }.get(execution_price, execution_price)

                params = {
                    'type': 'preset',
                    'name': strategy_name,
                    'short_window': config['short_window'],
                    'long_window': config['long_window'],
                    'data_dir': config['data_dir'],
                    'execution_price': execution_price,
                    'use_enhanced_logic': self.use_enhanced_logic.get(),
                    'use_cache': self.use_cache.get(),
                    'generate_charts': False,  # 批量运行时关闭图表生成
                    'save_interval': int(self.save_interval.get())
                }

                # 生成唯一任务ID
                task_id = str(uuid.uuid4())[:8]
                params['task_id'] = task_id

                # 创建完整的策略名称（包含执行价格）
                execution_suffix = {
                    'open': '_open',
                    'close': '_close',
                    'random': '_rand'
                }.get(execution_price, '_open')
                full_strategy_name = strategy_name + execution_suffix

                # 提交任务
                future = self.executor.submit(strategy_worker_process_graceful, params)
                self.futures[future] = task_id

                # 记录任务信息
                self.running_tasks[task_id] = {
                    'params': params,
                    'future': future,
                    'start_time': datetime.now(),
                    'status': '运行中',
                    'strategy_name': full_strategy_name
                }

                # 更新任务状态表
                display_name = f"{strategy_name}({execution_price_name})"
                self.update_task_status(task_id, display_name, '🔄 运行中', datetime.now().strftime("%H:%M:%S"))

                self.log_message(f"✅ 已添加任务: {display_name} (ID: {task_id})")

        self.log_message(f"📈 已提交所有 {total_strategies} 个任务，并行度: {mp.cpu_count()} 核")

    def graceful_stop_task(self, task_id):
        """优雅停止单个任务 - 等待当前股票处理完成后停止"""
        if task_id not in self.running_tasks:
            return

        strategy_name = self.running_tasks[task_id]['strategy_name']
        self.log_message(f"⏸️ 开始优雅停止任务: {strategy_name} (ID: {task_id})")
        self.log_message(f"   等待当前正在计算的股票完成后停止，并保存计算结果")

        # 创建优雅停止标志文件
        graceful_stop_flag_file = f"temp_graceful_stop_{task_id}.flag"
        try:
            with open(graceful_stop_flag_file, 'w') as f:
                f.write(f"graceful_stop_signal_{datetime.now().isoformat()}")

            # 更新任务状态
            self.update_task_status(task_id, None, '⏸️ 正在优雅停止...', None)
            self.running_tasks[task_id]['status'] = '正在优雅停止'

            self.log_message(f"📡 已发送优雅停止信号: {task_id}")

        except Exception as e:
            self.log_message(f"❌ 发送优雅停止信号失败: {task_id} - {str(e)}")



    def graceful_stop_all_tasks(self):
        """优雅停止所有运行中的任务"""
        if not self.running_tasks:
            messagebox.showinfo("提示", "当前没有运行中的任务")
            return

        # 更新超时时间
        try:
            self.graceful_stop_timeout = int(self.timeout_var.get())
        except ValueError:
            self.graceful_stop_timeout = 30

        task_count = len(self.running_tasks)
        confirm = messagebox.askyesno("确认优雅停止",
                                    f"确定要优雅停止所有 {task_count} 个运行中的任务吗？\n\n"
                                    f"系统将等待每个任务的当前股票处理完成后停止。\n"
                                    f"已处理的股票结果会被保存。\n"
                                    f"超时时间: {self.graceful_stop_timeout}秒")
        if not confirm:
            return

        self.log_message("⏸️ 开始优雅停止所有任务...")
        self.log_message(f"⏱️ 等待当前正在计算的股票完成后停止（超时: {self.graceful_stop_timeout}秒）...")

        # 发送停止信号给所有任务
        for task_id in list(self.running_tasks.keys()):
            self.graceful_stop_task(task_id)

        # 启动超时监控
        self.root.after(self.graceful_stop_timeout * 1000, self.check_graceful_stop_timeout)

    def check_graceful_stop_timeout(self):
        """检查优雅停止超时"""
        stopping_tasks = [tid for tid, info in self.running_tasks.items()
                         if info['status'] == '正在优雅停止']

        if stopping_tasks:
            self.log_message(f"⏰ 优雅停止超时，还有 {len(stopping_tasks)} 个任务未停止")
            choice = messagebox.askyesnocancel("超时处理",
                                             f"优雅停止超时，还有 {len(stopping_tasks)} 个任务未停止。\n\n"
                                             f"• 是：强制停止剩余任务\n"
                                             f"• 否：继续等待\n"
                                             f"• 取消：什么都不做")

            if choice is True:  # 强制停止
                for task_id in stopping_tasks:
                    self.force_stop_task(task_id)
                self.log_message("🛑 已强制停止所有超时任务")
            elif choice is False:  # 继续等待
                self.log_message("⏳ 继续等待任务优雅停止...")
                self.root.after(10000, self.check_graceful_stop_timeout)  # 再等10秒

    def force_stop_all_tasks(self):
        """强制停止所有运行中的任务"""
        if not self.running_tasks:
            messagebox.showinfo("提示", "当前没有运行中的任务")
            return

        task_count = len(self.running_tasks)
        confirm = messagebox.askyesno("确认强制停止",
                                    f"确定要强制停止所有 {task_count} 个运行中的任务吗？\n\n"
                                    f"这将立即终止所有任务，正在计算的股票结果不会保存。\n"
                                    f"已完成的股票结果会保留。")
        if not confirm:
            return

        self.log_message("🛑 开始强制停止所有任务...")
        self.log_message("   立即停止当前股票计算，不保存当前股票结果")

        # 尝试取消还未开始的任务
        cancelled_count = 0
        for task_id, task_info in list(self.running_tasks.items()):
            if task_info['future'].cancel():
                cancelled_count += 1
                self.update_task_status(task_id, None, '❌ 已取消', None)
                self.log_message(f"✅ 成功取消排队任务: {task_id}")

        # 对于正在运行的任务，强制停止
        running_count = task_count - cancelled_count
        if running_count > 0:
            self.log_message(f"🛑 强制停止 {running_count} 个运行中的任务...")

            # 关闭进程池强制终止
            self.executor.shutdown(wait=False)
            self.executor = ProcessPoolExecutor(max_workers=mp.cpu_count())

            # 更新剩余任务状态
            for task_id, task_info in self.running_tasks.items():
                if not task_info['future'].cancelled():
                    self.update_task_status(task_id, None, '🛑 强制停止', None)
                    self.log_message(f"🛑 强制停止任务: {task_id}")

        # 清理所有停止标志文件
        for task_id in self.running_tasks.keys():
            graceful_stop_flag_file = f"temp_graceful_stop_{task_id}.flag"
            force_stop_flag_file = f"temp_force_stop_{task_id}.flag"
            try:
                if os.path.exists(graceful_stop_flag_file):
                    os.remove(graceful_stop_flag_file)
                if os.path.exists(force_stop_flag_file):
                    os.remove(force_stop_flag_file)
            except:
                pass

        # 清理所有任务记录
        self.running_tasks.clear()
        self.futures.clear()

        # 显示停止结果
        self.log_message(f"⏹️ 停止完成！总计: {task_count} 个任务")
        self.log_message(f"   📊 取消排队: {cancelled_count} 个")
        self.log_message(f"   📊 强制停止: {running_count} 个")

        messagebox.showinfo("停止完成",
                          f"已成功停止所有任务！\n\n"
                          f"取消排队: {cancelled_count} 个\n"
                          f"强制停止: {running_count} 个")

    def update_task_status(self, task_id, strategy_name, status, start_time):
        """更新任务状态表"""
        # 查找是否已存在该任务
        for item in self.task_tree.get_children():
            if self.task_tree.item(item)['values'][0] == task_id:
                # 更新现有项
                current_values = list(self.task_tree.item(item)['values'])
                current_values[2] = status  # 更新状态
                if len(current_values) > 4:
                    # 计算耗时
                    if task_id in self.running_tasks:
                        elapsed = datetime.now() - self.running_tasks[task_id]['start_time']
                        current_values[4] = f"{elapsed.seconds}s"
                self.task_tree.item(item, values=current_values)
                return

        # 添加新项
        if strategy_name:
            self.task_tree.insert('', 'end', values=(
                task_id, strategy_name, status, start_time, "0s", "..."
            ))

    def monitor_tasks(self):
        """监控任务完成状态"""
        # 检查窗口状态和根窗口是否还存在
        try:
            if not self.window_active or not hasattr(self, 'root') or not self.root.winfo_exists():
                return
        except (tk.TclError, AttributeError):
            self.window_active = False
            return

        completed_tasks = []

        for future, task_id in list(self.futures.items()):
            if future.done():
                completed_tasks.append((future, task_id))

        # 处理完成的任务
        for future, task_id in completed_tasks:
            try:
                if future.cancelled():
                    # 任务被取消
                    self.update_task_status(task_id, None, '❌ 已取消', None)
                    self.log_message(f"❌ 任务已取消: {task_id}")
                    final_status = "已取消"
                else:
                    result = future.result()
                    if result['success']:
                        # 任务成功完成
                        results = result['results']

                        # 更新结果表
                        self.results_tree.insert('', 'end', values=(
                            results['strategy_name'],
                            results['stock_count'],
                            f"{results['avg_return']:.2%}",
                            f"{results['win_rate']:.1%}",
                            f"{results['max_return']:.2%}",
                            f"{results['min_return']:.2%}",
                            datetime.now().strftime("%H:%M:%S"),
                            "✅ 成功完成"
                        ))

                        # 更新任务状态
                        self.update_task_status(task_id, None, '✅ 完成', None)

                        self.log_message(f"✅ 任务完成: {results['strategy_name']} (ID: {task_id})")
                        self.log_message(f"   📊 平均收益率: {results['avg_return']:.2%}, 胜率: {results['win_rate']:.1%}")
                        final_status = "成功完成"

                    else:
                        # 任务失败或被优雅停止
                        error_msg = result['error']
                        if "优雅停止" in error_msg:
                            self.update_task_status(task_id, None, '⏸️ 优雅停止', None)
                            self.log_message(f"⏸️ 任务优雅停止: {task_id} - {error_msg}")
                            final_status = "优雅停止"
                        else:
                            self.update_task_status(task_id, None, '❌ 失败', None)
                            self.log_message(f"❌ 任务失败: {task_id} - {error_msg}")
                            final_status = "执行失败"

                        # 在结果表中也显示未完成的任务
                        strategy_name = self.running_tasks.get(task_id, {}).get('strategy_name', 'Unknown')
                        self.results_tree.insert('', 'end', values=(
                            strategy_name, "-", "-", "-", "-", "-",
                            datetime.now().strftime("%H:%M:%S"),
                            final_status
                        ))

            except Exception as e:
                # 异常处理（包括进程被强制终止的情况）
                error_str = str(e)
                if ("BrokenProcessPool" in str(type(e).__name__) or
                    "process" in error_str.lower() or
                    "terminated" in error_str.lower()):
                    self.update_task_status(task_id, None, '🛑 进程终止', None)
                    self.log_message(f"🛑 进程被终止: {task_id}")
                    final_status = "进程终止"
                else:
                    self.update_task_status(task_id, None, '💥 异常', None)
                    self.log_message(f"💥 任务异常: {task_id} - {error_str}")
                    final_status = "异常终止"

                # 在结果表中显示异常任务
                strategy_name = self.running_tasks.get(task_id, {}).get('strategy_name', 'Unknown')
                self.results_tree.insert('', 'end', values=(
                    strategy_name, "-", "-", "-", "-", "-",
                    datetime.now().strftime("%H:%M:%S"),
                    final_status
                ))

            # 清理
            if future in self.futures:
                del self.futures[future]
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

            # 清理停止标志文件
            graceful_stop_flag_file = f"temp_graceful_stop_{task_id}.flag"
            force_stop_flag_file = f"temp_force_stop_{task_id}.flag"
            try:
                if os.path.exists(graceful_stop_flag_file):
                    os.remove(graceful_stop_flag_file)
                if os.path.exists(force_stop_flag_file):
                    os.remove(force_stop_flag_file)
            except:
                pass

        # 更新CPU使用率显示
        try:
            if self.window_active and hasattr(self, 'cpu_usage_label'):
                active_tasks = len(self.running_tasks)
                cpu_percentage = min(100, (active_tasks / mp.cpu_count()) * 100)
                self.cpu_usage_label.config(text=f"活跃任务: {active_tasks}/{mp.cpu_count()} ({cpu_percentage:.1f}%)")
        except Exception as e:
            print(f"更新CPU使用率显示失败: {str(e)}")

        # 继续监控，添加更严格的状态检查并保存after_id
        try:
            if self.window_active and hasattr(self, 'root') and self.root.winfo_exists():
                after_id = self.root.after(1000, self.monitor_tasks)
                self.after_ids.append(after_id)
        except (tk.TclError, RuntimeError, AttributeError) as e:
            # 如果窗口已关闭或Tcl解释器已销毁，停止调度
            print(f"monitor_tasks停止调度: {str(e)}")
            self.window_active = False

    def validate_parameters(self):
        """验证策略参数"""
        if self.strategy_type.get() == "custom":
            try:
                short = int(self.short_window.get())
                long = int(self.long_window.get())
                if short >= long or short < 1 or long < 1:
                    messagebox.showerror("参数错误", "短期均线必须小于长期均线，且都必须大于0")
                    return False
            except ValueError:
                messagebox.showerror("参数错误", "请输入有效的数字")
                return False

        try:
            save_interval = int(self.save_interval.get())
            if save_interval < 1:
                messagebox.showerror("参数错误", "进度显示间隔必须大于0")
                return False
        except ValueError:
            messagebox.showerror("参数错误", "进度显示间隔必须是有效数字")
            return False

        return True

    def get_strategy_parameters(self):
        """获取策略参数"""
        if self.strategy_type.get() == "preset":
            strategy_name = self.preset_strategy.get()
            config = get_strategy_config(strategy_name)
            return {
                'type': 'preset',
                'name': strategy_name,
                'short_window': config['short_window'],
                'long_window': config['long_window'],
                'data_dir': config['data_dir'],
                'execution_price': self.execution_price.get(),
                'use_enhanced_logic': self.use_enhanced_logic.get(),
                'use_cache': self.use_cache.get(),
                'generate_charts': self.generate_charts.get(),
                'save_interval': int(self.save_interval.get())
            }
        else:
            return {
                'type': 'custom',
                'short_window': int(self.short_window.get()),
                'long_window': int(self.long_window.get()),
                'data_dir': "../日线数据",
                'execution_price': self.execution_price.get(),
                'use_enhanced_logic': self.use_enhanced_logic.get(),
                'use_cache': self.use_cache.get(),
                'generate_charts': self.generate_charts.get(),
                'save_interval': int(self.save_interval.get())
            }

    def check_queue(self):
        """检查线程队列中的消息"""
        # 检查窗口状态和根窗口是否还存在
        try:
            if not self.window_active or not hasattr(self, 'root') or not self.root.winfo_exists():
                return
        except (tk.TclError, AttributeError):
            self.window_active = False
            return

        try:
            while True:
                message_type, *args = self.thread_queue.get_nowait()

                if message_type == 'log':
                    self.log_message(args[0])

        except queue.Empty:
            pass
        except Exception as e:
            # 处理其他异常，避免程序崩溃
            print(f"check_queue异常: {str(e)}")

        # 继续检查，添加更严格的状态检查并保存after_id
        try:
            if self.window_active and hasattr(self, 'root') and self.root.winfo_exists():
                after_id = self.root.after(100, self.check_queue)
                self.after_ids.append(after_id)
        except (tk.TclError, RuntimeError, AttributeError) as e:
            # 如果窗口已关闭或Tcl解释器已销毁，停止调度
            print(f"check_queue停止调度: {str(e)}")
            self.window_active = False

    def open_cache_manager(self):
        """打开缓存管理窗口"""
        try:
            from 策略GUI界面 import CacheManagerWindow
            CacheManagerWindow(self.root)
        except ImportError:
            messagebox.showinfo("提示", "缓存管理功能需要策略GUI界面模块")
        except Exception as e:
            messagebox.showerror("错误", f"打开缓存管理器失败: {str(e)}")

    def open_multi_strategy_comparison(self):
        """打开多策略对比对话框 - 使用智能对齐GUI版本"""
        try:
            # 直接使用智能对齐GUI版本
            from 多策略收益率对比_智能对齐GUI import MultiStrategyComparisonWindow
            self.log_message("📊 打开多策略对比功能（智能对齐版）")
            MultiStrategyComparisonWindow(self.root)

        except ImportError as e:
            self.log_message(f"⚠️ 智能对齐版导入失败: {str(e)}")
            print(f"智能对齐版导入错误详情: {str(e)}")

            # 尝试导入真实平均曲线GUI版本
            try:
                from 多策略收益率对比_真实平均曲线GUI import MultiStrategyComparisonWindow
                self.log_message("📊 使用真实平均曲线版多策略对比功能")
                MultiStrategyComparisonWindow(self.root)

            except ImportError as e2:
                self.log_message(f"⚠️ 真实平均曲线版导入失败: {str(e2)}")

                # 尝试导入完全修复版
                try:
                    from 多策略收益率对比_完全修复版 import MultiStrategyComparisonWindow
                    self.log_message("📊 使用完全修复版多策略对比功能")
                    MultiStrategyComparisonWindow(self.root)

                except ImportError as e3:
                    self.log_message(f"⚠️ 完全修复版导入失败: {str(e3)}")

                    # 尝试使用普通修复版
                    try:
                        from 多策略收益率对比_修复版 import MultiStrategyComparisonWindow
                        self.log_message("📊 使用修复版多策略对比功能")
                        MultiStrategyComparisonWindow(self.root)

                    except ImportError as e4:
                        self.log_message(f"⚠️ 修复版也导入失败: {str(e4)}")

                        # 尝试使用原版
                        try:
                            from 多策略收益率对比 import MultiStrategyComparisonWindow
                            self.log_message("📊 使用原版多策略对比功能")
                            MultiStrategyComparisonWindow(self.root)

                        except ImportError as e5:
                            self.log_message(f"❌ 所有版本都导入失败: {str(e5)}")
                            messagebox.showerror("错误",
                                f"多策略对比功能模块未找到或导入失败！\n"
                                f"智能对齐版错误: {str(e)}\n"
                                f"真实平均曲线版错误: {str(e2)}\n"
                                f"完全修复版错误: {str(e3)}\n"
                                f"修复版错误: {str(e4)}\n"
                                f"原版错误: {str(e5)}\n\n"
                                f"请确保相关模块文件存在。")

        except Exception as e:
            self.log_message(f"❌ 打开多策略对比时出错: {str(e)}")
            print(f"错误详情: {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"打开多策略对比功能时出错:\n{str(e)}")

    def on_window_closing(self):
        """窗口关闭事件处理"""
        # 立即设置窗口状态为非活跃，停止after调度
        self.window_active = False

        # 取消所有pending的after调用
        print("🔄 正在取消所有after调度...")
        try:
            for after_id in self.after_ids:
                try:
                    self.root.after_cancel(after_id)
                except (tk.TclError, ValueError):
                    pass  # after_id可能已经执行完毕或无效
            self.after_ids.clear()
            print(f"✅ 已取消 {len(self.after_ids)} 个after调度")
        except Exception as e:
            print(f"⚠️ 取消after调度时出错: {str(e)}")

        if self.running_tasks:
            choice = messagebox.askyesnocancel("退出确认",
                                             f"还有{len(self.running_tasks)}个任务正在运行。\n\n"
                                             f"• 是(Y)：优雅停止所有任务并退出\n"
                                             f"• 否(N)：强制退出（不等待任务完成）\n"
                                             f"• 取消：继续运行")

            if choice is None:  # 取消
                self.window_active = True  # 恢复窗口状态
                # 重新启动监控
                try:
                    self.check_queue()
                    self.monitor_tasks()
                except:
                    pass
                return
            elif choice:  # 是 - 优雅停止
                try:
                    self.log_message("🚪 窗口关闭中，正在优雅停止所有任务...")

                    # 发送停止信号
                    for task_id in list(self.running_tasks.keys()):
                        self.graceful_stop_task(task_id)

                    # 等待短时间让任务优雅退出
                    start_time = time.time()
                    while self.running_tasks and time.time() - start_time < 10:  # 最多等10秒
                        try:
                            self.root.update()
                            time.sleep(0.2)
                        except (tk.TclError, RuntimeError):
                            break

                    if self.running_tasks:
                        self.log_message("⏰ 优雅停止超时，使用强制停止...")
                        try:
                            self.executor.shutdown(wait=False)
                        except:
                            pass
                    else:
                        self.log_message("✅ 所有任务已优雅停止")
                except Exception as e:
                    print(f"优雅停止过程中出错: {str(e)}")
            # 否 - 直接退出

        # 关闭执行器
        try:
            self.executor.shutdown(wait=False)
        except:
            pass

        # 清理所有停止标志文件
        try:
            for filename in glob.glob("temp_graceful_stop_*.flag"):
                try:
                    os.remove(filename)
                except:
                    pass
            for filename in glob.glob("temp_force_stop_*.flag"):
                try:
                    os.remove(filename)
                except:
                    pass
        except:
            pass

        # 最后一次确保取消所有after调度
        try:
            for after_id in self.after_ids:
                try:
                    self.root.after_cancel(after_id)
                except:
                    pass
            self.after_ids.clear()
        except:
            pass

        # 销毁窗口
        try:
            self.root.quit()
            self.root.destroy()
        except:
            pass


def main():
    """主函数"""
    # 设置多进程启动方法（Windows需要）
    if __name__ == "__main__":
        mp.set_start_method('spawn', force=True)

    # 直接启动tkinter主循环，不注册信号处理
    root = tk.Tk()
    app = GracefulStrategyGUI(root)
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n🛑 键盘中断，正在退出...")
        app.window_active = False
        try:
            app.executor.shutdown(wait=False)
        except:
            pass
        # 清理停止标志文件
        for filename in glob.glob("temp_graceful_stop_*.flag"):
            try:
                os.remove(filename)
            except:
                pass
        for filename in glob.glob("temp_force_stop_*.flag"):
            try:
                os.remove(filename)
            except:
                pass
    except Exception as e:
        print(f"程序异常: {str(e)}")
    finally:
        try:
            root.quit()
        except:
            pass


if __name__ == "__main__":
    main()