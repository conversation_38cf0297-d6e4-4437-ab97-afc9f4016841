#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试集成增强版的强制停止修复
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime

def test_enhanced_strategy_force_stop():
    """测试集成增强版策略的强制停止功能"""
    print("🧪 测试集成增强版策略的强制停止功能")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"📁 临时测试目录: {temp_dir}")
    
    try:
        # 导入策略类
        from 双均线策略_集成增强版 import MovingAverageStrategyEnhanced
        
        # 创建策略实例
        strategy = MovingAverageStrategyEnhanced(
            short_window=5,
            long_window=20,
            data_dir=temp_dir,
            execution_price='close'
        )
        
        print("✅ 成功导入并创建集成增强版策略实例")
        
        # 测试1: 检查方法签名
        print("\n🧪 测试1: 检查 save_single_stock_cache 方法签名")
        print("-" * 40)
        
        import inspect
        sig = inspect.signature(strategy.save_single_stock_cache)
        params = list(sig.parameters.keys())
        
        print(f"   方法参数: {params}")
        
        expected_params = ['self', 'stock_code', 'result', 'force_stop_check']
        if params == expected_params:
            print("   ✅ 方法签名正确，支持 force_stop_check 参数")
        else:
            print(f"   ❌ 方法签名不正确，期望: {expected_params}")
            return False
        
        # 测试2: 测试不带检查函数的调用（向后兼容）
        print("\n🧪 测试2: 测试向后兼容性（不带检查函数）")
        print("-" * 40)
        
        # 创建模拟结果
        mock_result = {
            'total_return': 0.15,
            'trade_count': 10,
            'win_rate': 0.6,
            'max_drawdown': 0.05
        }
        
        try:
            # 不传入检查函数
            success = strategy.save_single_stock_cache("TEST001", mock_result)
            if success:
                print("   ✅ 向后兼容性测试通过（不带检查函数）")
            else:
                print("   ❌ 向后兼容性测试失败")
                return False
        except Exception as e:
            print(f"   ❌ 向后兼容性测试异常: {str(e)}")
            return False
        
        # 测试3: 测试带检查函数的调用
        print("\n🧪 测试3: 测试带检查函数的调用")
        print("-" * 40)
        
        # 创建检查函数
        stop_requested = False
        
        def mock_force_stop_check():
            return stop_requested
        
        try:
            # 正常保存（无停止信号）
            stop_requested = False
            success = strategy.save_single_stock_cache("TEST002", mock_result, mock_force_stop_check)
            if success:
                print("   ✅ 正常保存测试通过（无停止信号）")
            else:
                print("   ❌ 正常保存测试失败")
                return False
            
            # 强制停止保存（有停止信号）
            stop_requested = True
            success = strategy.save_single_stock_cache("TEST003", mock_result, mock_force_stop_check)
            if not success:
                print("   ✅ 强制停止测试通过（有停止信号）")
            else:
                print("   ❌ 强制停止测试失败，应该返回False")
                return False
                
        except Exception as e:
            print(f"   ❌ 带检查函数测试异常: {str(e)}")
            return False
        
        # 测试4: 测试工作进程调用方式
        print("\n🧪 测试4: 测试工作进程调用方式")
        print("-" * 40)
        
        # 模拟工作进程中的调用方式
        task_id = "test_task"
        force_stop_flag_file = f"temp_force_stop_{task_id}.flag"
        
        def check_force_stop_signal():
            """模拟工作进程中的检查函数"""
            return os.path.exists(force_stop_flag_file)
        
        try:
            # 无停止信号的情况
            if os.path.exists(force_stop_flag_file):
                os.remove(force_stop_flag_file)
            
            success = strategy.save_single_stock_cache("TEST004", mock_result, check_force_stop_signal)
            if success:
                print("   ✅ 工作进程调用测试通过（无停止信号）")
            else:
                print("   ❌ 工作进程调用测试失败")
                return False
            
            # 有停止信号的情况
            with open(force_stop_flag_file, 'w') as f:
                f.write(f"force_stop_signal_{datetime.now().isoformat()}")
            
            success = strategy.save_single_stock_cache("TEST005", mock_result, check_force_stop_signal)
            if not success:
                print("   ✅ 工作进程停止测试通过（有停止信号）")
            else:
                print("   ❌ 工作进程停止测试失败，应该返回False")
                return False
            
            # 清理
            if os.path.exists(force_stop_flag_file):
                os.remove(force_stop_flag_file)
                
        except Exception as e:
            print(f"   ❌ 工作进程调用测试异常: {str(e)}")
            return False
        
        print("\n✅ 所有测试通过！集成增强版策略修复成功")
        return True
        
    except ImportError as e:
        print(f"❌ 导入策略类失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        return False
    finally:
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir)
            print(f"🧹 已清理临时目录: {temp_dir}")
        except:
            pass

def test_method_compatibility():
    """测试方法兼容性"""
    print("\n🧪 测试方法兼容性")
    print("=" * 60)
    
    try:
        from 双均线策略_集成增强版 import MovingAverageStrategyEnhanced
        from 双均线策略_增强版 import MovingAverageStrategyEnhanced as StandardStrategy
        
        # 检查两个版本的方法签名是否一致
        import inspect
        
        enhanced_sig = inspect.signature(MovingAverageStrategyEnhanced.save_single_stock_cache)
        standard_sig = inspect.signature(StandardStrategy.save_single_stock_cache)
        
        enhanced_params = list(enhanced_sig.parameters.keys())
        standard_params = list(standard_sig.parameters.keys())
        
        print(f"集成增强版参数: {enhanced_params}")
        print(f"标准版参数: {standard_params}")
        
        if enhanced_params == standard_params:
            print("✅ 两个版本的方法签名一致")
            return True
        else:
            print("❌ 两个版本的方法签名不一致")
            return False
            
    except Exception as e:
        print(f"❌ 兼容性测试异常: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试集成增强版策略修复")
    print("=" * 60)
    
    # 测试1: 集成增强版功能
    test1_result = test_enhanced_strategy_force_stop()
    
    # 测试2: 方法兼容性
    test2_result = test_method_compatibility()
    
    print("\n" + "=" * 60)
    print("🎯 测试结果总结:")
    print(f"  集成增强版功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"  方法兼容性测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！修复成功完成")
        print("💡 现在可以正常使用强制停止功能了")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        sys.exit(1)
