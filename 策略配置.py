#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
双均线策略配置文件
用户可以在这里修改策略参数
"""

# 双均线策略参数配置
STRATEGY_CONFIG = {
    # 均线参数
    'short_window': 5,      # 短期均线天数 (建议范围: 3-10)
    'long_window': 20,      # 长期均线天数 (建议范围: 15-60)
    
    # 数据路径
    'data_dir': '../日线数据',   # 股票数据目录（在父目录中）
    
    # 买入卖出执行价格类型
    'execution_price': 'open',  # 可选: 'open'(开盘价), 'close'(收盘价), 'random'(随机价)
    
    # 输出文件名
    'output_file': '双均线策略结果.csv'
}

# 执行价格类型说明
EXECUTION_PRICE_TYPES = {
    'open': {
        'name': '开盘价',
        'description': '信号第二天的开盘价执行',
        'suffix': 'open'
    },
    'close': {
        'name': '收盘价', 
        'description': '信号第二天的收盘价执行',
        'suffix': 'close'
    },
    'random': {
        'name': '随机价',
        'description': '信号第二天在最高价和最低价之间的随机价格执行',
        'suffix': 'rand'
    }
}

# 预设的策略参数组合
PRESET_STRATEGIES = {
    '激进策略': {
        'short_window': 3,
        'long_window': 10,
        'description': '短周期组合，信号更频繁但可能假信号较多'
    },
    
    '稳健策略': {
        'short_window': 5,
        'long_window': 20,
        'description': '中等周期组合，平衡信号频率和准确性'
    },
    
    '保守策略': {
        'short_window': 10,
        'long_window': 30,
        'description': '长周期组合，信号较少但更可靠'
    },
    
    '超保守策略': {
        'short_window': 20,
        'long_window': 60,
        'description': '超长周期组合，适合长期投资'
    }
}

def get_strategy_config(strategy_name='稳健策略'):
    """
    获取指定策略的配置
    
    Args:
        strategy_name: 策略名称
        
    Returns:
        dict: 策略配置
    """
    if strategy_name in PRESET_STRATEGIES:
        config = STRATEGY_CONFIG.copy()
        config.update(PRESET_STRATEGIES[strategy_name])
        return config
    else:
        return STRATEGY_CONFIG

def list_available_strategies():
    """列出所有可用的预设策略"""
    print("可用的预设策略:")
    print("-" * 50)
    for name, config in PRESET_STRATEGIES.items():
        print(f"{name}: MA{config['short_window']}-MA{config['long_window']}")
        print(f"  说明: {config['description']}")
        print() 