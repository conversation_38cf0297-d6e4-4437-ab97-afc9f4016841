#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试添加历史任务功能
创建一些测试历史任务，然后测试添加历史任务的功能
"""

import sys
import os
from datetime import datetime, timedelta

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from 任务管理器 import task_manager


def create_test_historical_tasks():
    """创建一些测试历史任务"""
    print("🧪 创建测试历史任务...")
    
    # 测试任务配置列表
    test_tasks = [
        {
            'name': '激进策略',
            'type': 'preset',
            'short_window': 3,
            'long_window': 10,
            'data_dir': '../日线数据',
            'execution_price': 'open',
            'use_enhanced_logic': True,
            'use_cache': True,
            'generate_charts': False,
            'save_interval': 20,
            'status': 'completed',
            'progress': {'total_stocks': 1000, 'processed_stocks': 1000, 'successful_stocks': 850},
            'results': {'avg_return': 0.12, 'win_rate': 0.62, 'max_return': 0.45, 'min_return': -0.25}
        },
        {
            'name': '稳健策略',
            'type': 'preset',
            'short_window': 5,
            'long_window': 20,
            'data_dir': '../日线数据',
            'execution_price': 'close',
            'use_enhanced_logic': True,
            'use_cache': True,
            'generate_charts': False,
            'save_interval': 20,
            'status': 'paused',
            'progress': {'total_stocks': 1000, 'processed_stocks': 650, 'successful_stocks': 520},
            'results': {'avg_return': 0.08, 'win_rate': 0.58, 'max_return': 0.35, 'min_return': -0.18}
        },
        {
            'name': '保守策略',
            'type': 'preset',
            'short_window': 10,
            'long_window': 30,
            'data_dir': '../日线数据',
            'execution_price': 'random',
            'use_enhanced_logic': True,
            'use_cache': True,
            'generate_charts': False,
            'save_interval': 20,
            'status': 'failed',
            'progress': {'total_stocks': 1000, 'processed_stocks': 200, 'successful_stocks': 150},
            'results': {'avg_return': 0.05, 'win_rate': 0.55, 'max_return': 0.28, 'min_return': -0.15}
        },
        {
            'name': '自定义策略',
            'type': 'custom',
            'short_window': 7,
            'long_window': 25,
            'data_dir': '../日线数据',
            'execution_price': 'open',
            'use_enhanced_logic': False,
            'use_cache': True,
            'generate_charts': True,
            'save_interval': 50,
            'status': 'created',
            'progress': {'total_stocks': 0, 'processed_stocks': 0, 'successful_stocks': 0},
            'results': {'avg_return': 0.0, 'win_rate': 0.0, 'max_return': 0.0, 'min_return': 0.0}
        }
    ]
    
    created_tasks = []
    
    for i, task_params in enumerate(test_tasks):
        try:
            # 分离任务参数和状态信息
            params = {k: v for k, v in task_params.items() 
                     if k not in ['status', 'progress', 'results']}
            
            # 创建任务
            task_id = task_manager.create_task(params)
            created_tasks.append(task_id)
            
            # 设置创建时间（模拟不同时间创建的任务）
            created_time = (datetime.now() - timedelta(days=i+1)).isoformat()
            task_manager.update_task_status(task_id, task_params['status'], 
                                          created_time=created_time)
            
            # 更新进度
            progress = task_params['progress']
            task_manager.update_task_progress(
                task_id, 
                total_stocks=progress['total_stocks'],
                processed_stocks=progress['processed_stocks'],
                successful_stocks=progress['successful_stocks']
            )
            
            # 更新结果
            results = task_params['results']
            task_manager.update_task_status(task_id, task_params['status'],
                                          **{f'results.{k}': v for k, v in results.items()})
            
            # 添加一些测试日志
            task_manager.add_execution_log(task_id, f"任务创建: {params['name']}", 'info')
            
            if task_params['status'] == 'completed':
                task_manager.add_execution_log(task_id, "任务成功完成", 'info')
            elif task_params['status'] == 'paused':
                task_manager.add_execution_log(task_id, "任务被用户暂停", 'warning')
            elif task_params['status'] == 'failed':
                task_manager.add_execution_log(task_id, "任务执行失败", 'error')
            
            print(f"✅ 创建测试任务: {params['name']} (ID: {task_id}, 状态: {task_params['status']})")
            
        except Exception as e:
            print(f"❌ 创建测试任务失败: {str(e)}")
    
    return created_tasks


def display_test_tasks():
    """显示测试任务信息"""
    print("\n📋 当前历史任务列表:")
    print("-" * 80)
    
    all_tasks = task_manager.get_all_tasks()
    
    if not all_tasks:
        print("没有找到历史任务")
        return
    
    # 表头
    print(f"{'任务ID':<10} {'策略名称':<15} {'状态':<10} {'进度':<15} {'平均收益率':<10} {'创建时间':<20}")
    print("-" * 80)
    
    for task in all_tasks:
        task_id = task.get('task_id', 'Unknown')[:8]
        
        # 构建策略名称
        params = task.get('parameters', {})
        strategy_name = params.get('name', f"MA{params.get('short_window', '?')}-{params.get('long_window', '?')}")
        execution_price = params.get('execution_price', 'open')
        execution_suffix = {'open': '_open', 'close': '_close', 'random': '_rand'}.get(execution_price, '_open')
        full_strategy_name = (strategy_name + execution_suffix)[:15]
        
        status = task.get('status', 'unknown')
        
        # 计算进度
        progress = task.get('progress', {})
        total_stocks = progress.get('total_stocks', 0)
        processed_stocks = progress.get('processed_stocks', 0)
        if total_stocks > 0:
            progress_text = f"{processed_stocks}/{total_stocks}"
        else:
            progress_text = "0/0"
        
        # 获取结果
        results = task.get('results', {})
        avg_return = results.get('avg_return', 0.0)
        
        # 格式化创建时间
        created_time = task.get('created_time', '')
        try:
            dt = datetime.fromisoformat(created_time)
            formatted_time = dt.strftime("%m-%d %H:%M:%S")
        except:
            formatted_time = created_time[:19] if created_time else 'Unknown'
        
        print(f"{task_id:<10} {full_strategy_name:<15} {status:<10} {progress_text:<15} {avg_return:>8.2%} {formatted_time:<20}")


def test_task_filtering():
    """测试任务过滤功能"""
    print("\n🧪 测试任务过滤功能...")
    
    all_tasks = task_manager.get_all_tasks()
    print(f"总任务数: {len(all_tasks)}")
    
    # 按状态过滤
    completed_tasks = task_manager.get_tasks_by_status('completed')
    paused_tasks = task_manager.get_tasks_by_status('paused')
    failed_tasks = task_manager.get_tasks_by_status('failed')
    created_tasks = task_manager.get_tasks_by_status('created')
    
    print(f"已完成任务: {len(completed_tasks)}")
    print(f"已暂停任务: {len(paused_tasks)}")
    print(f"失败任务: {len(failed_tasks)}")
    print(f"已创建任务: {len(created_tasks)}")
    
    # 模拟GUI中的过滤逻辑
    available_tasks = []
    running_task_ids = set()  # 模拟正在运行的任务ID
    
    for task in all_tasks:
        task_id = task.get('task_id')
        status = task.get('status', 'unknown')
        
        # 排除正在运行的任务
        if task_id not in running_task_ids and status != 'running':
            available_tasks.append(task)
    
    print(f"可添加到队列的任务: {len(available_tasks)}")
    
    for task in available_tasks:
        task_id = task.get('task_id', 'Unknown')[:8]
        params = task.get('parameters', {})
        strategy_name = params.get('name', 'Unknown')
        status = task.get('status', 'unknown')
        print(f"  - {task_id}: {strategy_name} ({status})")


def cleanup_test_tasks():
    """清理测试任务（可选）"""
    print("\n🧹 清理测试任务...")
    
    all_tasks = task_manager.get_all_tasks()
    
    for task in all_tasks:
        task_id = task.get('task_id')
        params = task.get('parameters', {})
        strategy_name = params.get('name', '')
        
        # 只删除测试任务
        if strategy_name in ['激进策略', '稳健策略', '保守策略', '自定义策略']:
            if task_manager.delete_task(task_id):
                print(f"🗑️ 删除测试任务: {strategy_name} (ID: {task_id})")


def main():
    """主测试函数"""
    print("🚀 测试添加历史任务功能")
    print("=" * 60)
    
    try:
        # 创建测试历史任务
        created_tasks = create_test_historical_tasks()
        
        # 显示任务列表
        display_test_tasks()
        
        # 测试任务过滤
        test_task_filtering()
        
        print("\n" + "=" * 60)
        print("✅ 测试数据创建完成！")
        print("\n📋 使用说明:")
        print("1. 启动策略GUI优雅版程序")
        print("2. 点击 '📋 添加历史任务' 按钮")
        print("3. 在弹出的对话框中选择要运行的历史任务")
        print("4. 点击 '确定添加' 将选中的任务添加到运行队列")
        print("5. 观察任务状态监控面板中的任务执行情况")
        
        print(f"\n📁 任务配置文件位置: {task_manager.tasks_dir}")
        print(f"📄 创建的测试任务数量: {len(created_tasks)}")
        
        # 询问是否清理测试数据
        print("\n" + "=" * 60)
        choice = input("是否要清理测试任务数据？(y/N): ").strip().lower()
        if choice == 'y':
            cleanup_test_tasks()
            print("✅ 测试数据已清理")
        else:
            print("📝 测试数据保留，可用于GUI测试")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
