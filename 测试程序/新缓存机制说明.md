# 新缓存机制说明

## 概述

已成功修改缓存模式，实现了每个测算在cache目录下建立独立目录，每个股票测算完成后立即保存为单独的pkl文件的需求。

## 主要改进

### 1. 目录结构变化

**旧结构：**
```
cache/
├── strategy_cache_MA5_20_open.pkl
├── strategy_cache_MA5_20_close.pkl
└── strategy_cache_MA10_30_open.pkl
```

**新结构：**
```
cache/
├── MA5_20_open/
│   ├── 000001.pkl
│   ├── 000002.pkl
│   └── 600000.pkl
├── MA5_20_close/
│   ├── 000001.pkl
│   ├── 000002.pkl
│   └── 600000.pkl
└── MA10_30_open/
    ├── 000001.pkl
    ├── 000002.pkl
    └── 600000.pkl
```

### 2. 实时缓存机制

- **立即保存**：每个股票测算完成后立即保存，不再等待N个股票一起保存
- **原子操作**：使用临时文件(.tmp)确保写入安全，避免文件损坏
- **完整性验证**：保存后立即验证文件完整性

### 3. 缓存文件格式

每个股票的缓存文件包含：
```python
{
    'result': {
        'stock_code': '000001',
        'total_return': 0.15,
        'trade_count': 8,
        'trades': [...],
        'equity_curve': [...],
        'delayed_buy_count': 2,
        'delayed_sell_count': 1
    },
    'file_hash': 'md5_hash_of_source_file',
    'parameters': {
        'short_window': 5,
        'long_window': 20,
        'execution_price': 'open'
    },
    'cache_time': '2024-01-01T12:00:00'
}
```

## 核心功能

### 1. 单个股票缓存保存
```python
def save_single_stock_cache(self, stock_code, result):
    """保存单个股票的缓存数据到独立文件"""
    # 使用临时文件安全保存
    # 验证文件完整性
    # 原子性替换
```

### 2. 单个股票缓存加载
```python
def load_single_stock_cache(self, stock_code):
    """加载单个股票的缓存数据"""
    # 验证参数匹配
    # 检查文件变化
    # 返回有效结果或None
```

### 3. 批量缓存管理
```python
def load_individual_cache(self):
    """从单个股票缓存文件加载数据"""
    # 检查所有股票文件
    # 加载有效缓存
    # 返回需要重新计算的文件列表
```

### 4. 旧缓存迁移
```python
def migrate_legacy_cache(self):
    """将旧格式的缓存文件迁移到新格式"""
    # 自动检测旧缓存文件
    # 迁移到新的单个文件格式
    # 备份原文件
```

## 优势对比

| 特性 | 旧机制 | 新机制 |
|------|--------|--------|
| 保存时机 | 每N个股票批量保存 | 每个股票立即保存 |
| 文件结构 | 单个大文件 | 多个小文件 |
| 数据安全 | 程序中断可能丢失数据 | 实时保存，数据更安全 |
| 文件损坏影响 | 影响所有股票 | 只影响单个股票 |
| 缓存加载 | 全量加载 | 按需加载 |
| 目录组织 | 平铺结构 | 按策略分组 |

## 兼容性

### 1. 自动迁移
- 系统会自动检测旧格式缓存文件
- 首次运行时自动迁移到新格式
- 旧文件会被重命名为`.backup`保留

### 2. 向后兼容
- 保留了`save_cache()`方法用于兼容性
- 工作进程无需修改即可使用新机制

## 使用方法

### 1. 正常使用

```python
# 创建策略实例
strategy = MovingAverageStrategyEnhanced(
    short_window=5,
    long_window=20,
    data_dir="../../日线数据",
    execution_price='open'
)

# 运行策略（自动使用新缓存机制）
strategy.run_strategy(use_cache=True)
```

### 2. 清理缓存
```python
# 清理当前策略的所有缓存
strategy.clear_cache()
```

### 3. 测试新机制
```python
# 运行测试脚本
python 测试新缓存机制.py
```

## 文件修改清单

### 1. 主要文件
- `双均线策略_增强版.py` - 主策略文件，完整实现新缓存机制
- `双均线策略_集成增强版.py` - 集成版策略文件，同步新机制

### 2. 新增方法
- `save_single_stock_cache()` - 保存单个股票缓存
- `load_single_stock_cache()` - 加载单个股票缓存
- `load_individual_cache()` - 批量加载缓存
- `migrate_legacy_cache()` - 迁移旧缓存

### 3. 修改方法
- `__init__()` - 修改缓存目录结构
- `run_strategy()` - 修改为实时保存
- `clear_cache()` - 支持新目录结构

## 测试验证

运行测试脚本验证新机制：
```bash
python 测试新缓存机制.py
```

测试内容包括：
1. 实时缓存保存功能
2. 缓存加载和性能提升
3. 缓存文件完整性验证
4. 缓存清理功能
5. 旧缓存文件迁移

## 注意事项

1. **磁盘空间**：新机制会创建更多小文件，确保有足够磁盘空间
2. **文件系统**：某些文件系统对小文件数量有限制
3. **备份策略**：建议定期备份整个cache目录
4. **清理策略**：可以定期清理过期的缓存目录

## 故障排除

### 1. 缓存目录权限问题
```bash
# 确保有写入权限
chmod 755 cache/
```

### 2. 旧缓存迁移失败
- 检查旧缓存文件是否损坏
- 手动删除损坏的旧缓存文件
- 重新运行程序

### 3. 缓存文件损坏
- 单个股票缓存损坏只影响该股票
- 系统会自动重新计算损坏的股票
- 使用缓存修复工具检查和清理

## 总结

新的缓存机制实现了以下目标：
✅ 每个测算在cache目录下建立独立目录
✅ 每个股票测算完成后立即保存为单独的pkl文件
✅ 提高了数据安全性和系统稳定性
✅ 保持了向后兼容性
✅ 提供了完整的测试和验证工具
