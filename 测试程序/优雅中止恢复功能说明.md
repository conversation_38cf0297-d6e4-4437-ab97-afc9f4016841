# 优雅中止恢复功能说明

## 功能概述

优雅中止恢复功能允许用户在策略计算过程中优雅地暂停任务，并在稍后恢复继续运行，而不会丢失已经计算的结果。

## 核心特性

### 1. 优雅暂停
- **智能停止点**：在当前股票处理完成后停止，不会中断正在进行的计算
- **结果保护**：已处理的股票结果通过实时缓存机制立即保存
- **状态保存**：保存任务的完整状态信息，包括已处理和剩余的股票列表

### 2. 状态持久化
- **暂停状态文件**：`temp_pause_state_{task_id}.json`
- **包含信息**：
  - 任务ID和参数
  - 已处理的文件列表
  - 剩余待处理的文件列表
  - 暂停时间和策略名称

### 3. 恢复机制
- **无缝恢复**：从暂停点继续，只处理剩余的股票
- **结果合并**：自动合并之前的缓存结果和新计算的结果
- **状态清理**：恢复完成后自动清理暂停状态文件

## 使用方法

### 1. 暂停任务

#### 方法一：优雅停止按钮
1. 在策略GUI中点击"⏸️ 优雅停止"按钮
2. 系统会等待所有运行中的任务完成当前股票后停止
3. 任务状态变为"⏸️ 已暂停"

#### 方法二：右键菜单
1. 在任务状态表中右键点击要停止的任务
2. 选择"⏸️ 优雅停止该任务"
3. 该任务会在完成当前股票后暂停

### 2. 恢复任务

#### 方法一：批量恢复按钮
1. 点击"▶️ 恢复暂停任务"按钮
2. 系统会自动找到所有暂停的任务
3. 确认后批量恢复所有暂停的任务

#### 方法二：右键菜单恢复
1. 在任务状态表中右键点击暂停的任务
2. 选择"▶️ 恢复运行该任务"
3. 确认后恢复该任务

### 3. 管理暂停状态

#### 查看暂停状态
- 使用测试程序"测试优雅中止恢复功能.py"
- 可以查看所有暂停任务的详细信息

#### 清理暂停状态
1. 右键点击任务选择"🗑️ 清理暂停状态"
2. 或使用测试程序批量清理
3. 清理后无法恢复该任务

## 技术实现

### 1. 暂停机制
```python
# 检查优雅停止信号
graceful_stop_requested = check_graceful_stop_signal()
if graceful_stop_requested:
    # 保存暂停状态
    remaining_files = files_to_process[i:]
    save_pause_state(processed_files, remaining_files, strategy.results)
    
    return {
        'success': False,
        'task_id': task_id,
        'error': f"任务被优雅暂停，已处理 {processed_count} 只股票，可恢复继续运行",
        'paused': True
    }
```

### 2. 状态保存
```python
def save_pause_state(processed_files, remaining_files, current_results):
    pause_data = {
        'task_id': task_id,
        'params': params,
        'processed_files': processed_files,
        'remaining_files': remaining_files,
        'current_results_count': len(current_results),
        'pause_time': datetime.now().isoformat(),
        'strategy_name': params.get('name', f"MA{params['short_window']}-MA{params['long_window']}")
    }
    
    with open(f"temp_pause_state_{task_id}.json", 'w', encoding='utf-8') as f:
        json.dump(pause_data, f, ensure_ascii=False, indent=2)
```

### 3. 恢复处理
```python
def resume_strategy_worker_process(task_id):
    # 加载暂停状态
    with open(f"temp_pause_state_{task_id}.json", 'r', encoding='utf-8') as f:
        pause_data = json.load(f)
    
    # 获取剩余文件
    remaining_files = pause_data['remaining_files']
    
    # 修改参数为恢复模式
    resume_params = params.copy()
    resume_params['resume_mode'] = True
    resume_params['remaining_files'] = remaining_files
    
    # 调用原始工作进程处理剩余文件
    return strategy_worker_process_graceful(resume_params)
```

## 文件说明

### 核心文件
- `策略工作进程_优雅版.py`：包含暂停和恢复逻辑
- `策略GUI优雅版.py`：GUI界面，支持暂停和恢复操作

### 测试文件
- `测试程序/测试优雅中止恢复功能.py`：暂停状态管理工具
- `测试程序/优雅中止恢复功能说明.md`：本说明文档

### 临时文件
- `temp_graceful_stop_{task_id}.flag`：优雅停止信号文件
- `temp_pause_state_{task_id}.json`：暂停状态数据文件

## 注意事项

### 1. 数据安全
- 已处理的股票结果通过实时缓存机制保存，不会丢失
- 暂停状态文件包含完整的恢复信息
- 建议定期备份缓存目录

### 2. 性能考虑
- 暂停和恢复操作几乎无性能损失
- 实时缓存确保每只股票处理完立即保存
- 恢复时只处理剩余股票，避免重复计算

### 3. 使用建议
- 长时间运行的任务建议使用优雅停止而非强制终止
- 定期清理不需要的暂停状态文件
- 系统重启前可以暂停所有任务，重启后恢复

## 故障排除

### 1. 无法恢复任务
- 检查暂停状态文件是否存在
- 确认文件格式是否正确
- 查看日志了解具体错误信息

### 2. 恢复后结果不完整
- 检查缓存目录是否完整
- 确认策略参数是否一致
- 验证数据文件是否有变化

### 3. 暂停状态文件损坏
- 使用测试程序查看文件内容
- 手动删除损坏的状态文件
- 重新运行策略任务

## 更新日志

### v1.0 (当前版本)
- 实现基本的优雅暂停和恢复功能
- 支持批量恢复和状态管理
- 提供测试工具和详细文档

### 未来计划
- 支持任务优先级和调度
- 添加暂停状态的自动清理机制
- 实现跨会话的任务恢复功能
