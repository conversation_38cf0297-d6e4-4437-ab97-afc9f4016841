#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试多策略比较工具的修复情况
"""

import os
import sys

def test_unicode_fix():
    """测试Unicode编码修复"""
    print("测试多策略比较工具的Unicode编码修复")
    print("=" * 50)
    
    try:
        # 尝试导入修复后的模块
        import multi_strategy_compare
        print("✓ 成功导入 multi_strategy_compare 模块")
        
        # 测试主要函数是否存在
        functions_to_test = [
            'run_strategy',
            'run_multi_strategy_compare', 
            'create_comparison_charts',
            'display_comparison_summary',
            'create_default_strategy_configs',
            'main'
        ]
        
        for func_name in functions_to_test:
            if hasattr(multi_strategy_compare, func_name):
                print(f"✓ 函数 {func_name} 存在")
            else:
                print(f"✗ 函数 {func_name} 缺失")
        
        # 测试创建默认配置
        configs = multi_strategy_compare.create_default_strategy_configs()
        print(f"✓ 成功创建默认配置，共 {len(configs)} 个策略")
        
        for config in configs:
            print(f"  - {config['name']}: MA{config['short_window']}-{config['long_window']}, {config['execution_price']}")
        
        print("\n✓ Unicode编码问题已修复！")
        print("✓ 多策略比较工具可以正常使用")
        
        return True
        
    except UnicodeEncodeError as e:
        print(f"✗ Unicode编码错误仍然存在: {str(e)}")
        return False
    except ImportError as e:
        print(f"✗ 导入错误: {str(e)}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {str(e)}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n测试GUI集成")
    print("=" * 30)
    
    try:
        # 检查GUI文件是否存在
        gui_file = "策略GUI优雅版.py"
        if os.path.exists(gui_file):
            print(f"✓ GUI文件存在: {gui_file}")
            
            # 检查GUI中的多策略对比功能
            with open(gui_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'multi_strategy_compare.py' in content:
                print("✓ GUI中已集成新的多策略比较工具")
            else:
                print("? GUI中可能使用其他版本的多策略比较工具")
                
            if 'open_multi_strategy_comparison' in content:
                print("✓ GUI中存在多策略对比功能")
            else:
                print("✗ GUI中缺少多策略对比功能")
                
        else:
            print(f"✗ GUI文件不存在: {gui_file}")
            
    except Exception as e:
        print(f"✗ 检查GUI集成时出错: {str(e)}")

def show_usage_instructions():
    """显示使用说明"""
    print("\n使用说明")
    print("=" * 30)
    print("1. 在策略GUI中点击'多策略对比'按钮")
    print("2. 选择'是'使用新版多策略对比工具")
    print("3. 工具将自动运行并生成对比结果")
    print("4. 结果保存在 charts/ 目录中")
    print("\n或者直接运行:")
    print("python multi_strategy_compare.py")

if __name__ == "__main__":
    print("多策略比较工具修复验证")
    print("=" * 60)
    
    # 测试Unicode修复
    unicode_ok = test_unicode_fix()
    
    # 测试GUI集成
    test_gui_integration()
    
    # 显示使用说明
    show_usage_instructions()
    
    print("\n" + "=" * 60)
    if unicode_ok:
        print("✓ 修复验证完成！多策略比较工具已可正常使用")
    else:
        print("✗ 仍存在问题，请检查错误信息") 