#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试移除强制停止功能后的优雅停止机制
"""

import os
import time
import threading
from datetime import datetime

def test_graceful_stop_only():
    """测试只保留优雅停止功能"""
    print("🧪 测试优雅停止功能（已移除强制停止）")
    print("=" * 60)
    
    # 模拟任务ID
    task_id = "test_graceful_only"
    
    print(f"📋 测试任务ID: {task_id}")
    print()
    
    # 测试1: 检查只有优雅停止标志文件
    print("🧪 测试1: 检查停止标志文件")
    print("-" * 40)
    
    graceful_stop_flag_file = f"temp_graceful_stop_{task_id}.flag"
    force_stop_flag_file = f"temp_force_stop_{task_id}.flag"  # 应该不再使用
    
    def check_graceful_stop_signal():
        """检查优雅停止信号文件"""
        return os.path.exists(graceful_stop_flag_file)
    
    def cleanup_stop_flag():
        """清理停止标志文件"""
        try:
            if os.path.exists(graceful_stop_flag_file):
                os.remove(graceful_stop_flag_file)
        except:
            pass
    
    # 创建优雅停止标志文件
    print(f"📝 创建优雅停止标志文件: {graceful_stop_flag_file}")
    with open(graceful_stop_flag_file, 'w') as f:
        f.write(f"graceful_stop_signal_{datetime.now().isoformat()}")
    
    # 检查文件是否存在
    if os.path.exists(graceful_stop_flag_file):
        print("✅ 优雅停止标志文件创建成功")
    else:
        print("❌ 优雅停止标志文件创建失败")
        return False
    
    # 确认不会创建强制停止文件
    if not os.path.exists(force_stop_flag_file):
        print("✅ 确认没有强制停止标志文件（已移除）")
    else:
        print("❌ 意外发现强制停止标志文件")
        return False
    
    # 清理
    cleanup_stop_flag()
    print("🧹 已清理优雅停止标志文件")
    
    print()
    
    # 测试2: 模拟优雅停止工作流程
    print("🧪 测试2: 模拟优雅停止工作流程")
    print("-" * 40)
    
    # 模拟股票处理循环
    def simulate_stock_processing():
        """模拟股票处理过程"""
        stocks = ["000001", "000002", "000003", "000004", "000005"]
        processed_count = 0
        
        for i, stock_code in enumerate(stocks, 1):
            print(f"   📊 正在处理股票 ({i}/{len(stocks)}): {stock_code}")
            
            # 在开始处理每只股票前检查优雅停止信号
            graceful_stop_requested = check_graceful_stop_signal()
            if graceful_stop_requested:
                print(f"   ⏸️ 收到优雅停止信号，等待当前股票处理完成后停止")
            
            # 模拟股票处理时间
            time.sleep(0.1)
            
            # 模拟处理完成
            processed_count += 1
            print(f"   ✅ {stock_code} 处理完成，结果已保存")
            
            # 在当前股票处理完成后，检查是否收到优雅停止信号
            if graceful_stop_requested:
                print(f"   ⏸️ 当前股票 {stock_code} 处理完成，响应优雅停止信号")
                print(f"   ✅ 已实时保存 {processed_count} 只股票的计算结果")
                cleanup_stop_flag()
                return processed_count
        
        print(f"   📈 所有股票处理完成，共处理了 {processed_count} 只股票")
        return processed_count
    
    def send_graceful_stop_after_delay(delay):
        """延迟发送优雅停止信号"""
        time.sleep(delay)
        print(f"   📡 发送优雅停止信号...")
        with open(graceful_stop_flag_file, 'w') as f:
            f.write(f"graceful_stop_signal_{datetime.now().isoformat()}")
    
    # 测试在处理第2只股票时发送停止信号
    stop_thread = threading.Thread(target=send_graceful_stop_after_delay, args=(0.15,))
    stop_thread.start()
    
    processed = simulate_stock_processing()
    stop_thread.join()
    
    if processed == 2:
        print("   ✅ 优雅停止工作正常，在第2只股票完成后停止")
    else:
        print(f"   ❌ 优雅停止异常，处理了 {processed} 只股票")
        return False
    
    print()
    
    # 测试3: 验证GUI界面更新
    print("🧪 测试3: 验证界面功能")
    print("-" * 40)
    
    print("   ✅ 界面应该只显示'⏸️ 优雅停止'按钮")
    print("   ✅ 右键菜单应该只有'⏸️ 优雅停止该任务'选项")
    print("   ✅ 确认对话框应该说明优雅停止的行为")
    print("   ✅ 不再有强制停止相关的界面元素")
    
    print()
    
    # 测试4: 验证策略类方法
    print("🧪 测试4: 验证策略类方法")
    print("-" * 40)
    
    try:
        from 双均线策略_集成增强版 import MovingAverageStrategyEnhanced
        
        # 检查方法签名
        import inspect
        sig = inspect.signature(MovingAverageStrategyEnhanced.save_single_stock_cache)
        params = list(sig.parameters.keys())
        
        expected_params = ['self', 'stock_code', 'result']
        if params == expected_params:
            print("   ✅ 集成增强版方法签名正确（已移除force_stop_check参数）")
        else:
            print(f"   ❌ 集成增强版方法签名错误: {params}")
            return False
        
        # 测试标准版
        from 双均线策略_增强版 import MovingAverageStrategyEnhanced as StandardStrategy
        
        sig2 = inspect.signature(StandardStrategy.save_single_stock_cache)
        params2 = list(sig2.parameters.keys())
        
        if params2 == expected_params:
            print("   ✅ 标准版方法签名正确（已移除force_stop_check参数）")
        else:
            print(f"   ❌ 标准版方法签名错误: {params2}")
            return False
        
        # 检查两个版本是否一致
        if params == params2:
            print("   ✅ 两个版本的方法签名一致")
        else:
            print("   ❌ 两个版本的方法签名不一致")
            return False
            
    except Exception as e:
        print(f"   ❌ 策略类测试异常: {str(e)}")
        return False
    
    print()
    print("✅ 优雅停止功能测试完成！")
    print("=" * 60)
    print("🎯 清理结果总结:")
    print("  ✅ 已移除所有强制停止相关代码")
    print("  ✅ 保留完整的优雅停止功能")
    print("  ✅ 界面简化，只保留必要的停止选项")
    print("  ✅ 策略类方法签名已简化")
    print("  ✅ 工作进程逻辑已简化")
    print("  ✅ 文件清理逻辑已简化")
    
    return True

if __name__ == "__main__":
    print("🚀 开始测试优雅停止功能（强制停止已移除）")
    print("=" * 60)
    
    success = test_graceful_stop_only()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！强制停止功能已成功移除")
        print("💡 现在系统只保留优雅停止功能，更简洁可靠")
    else:
        print("❌ 部分测试失败，需要进一步检查")
        exit(1)
