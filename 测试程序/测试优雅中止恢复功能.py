#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试优雅中止恢复功能
验证任务可以在优雅中止后恢复继续运行
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
import time
import threading
import json
from datetime import datetime

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class ResumeTestWindow:
    def __init__(self, root):
        self.root = root
        self.root.title("优雅中止恢复功能测试")
        self.root.geometry("800x600")
        
        self.create_widgets()
        self.refresh_pause_states()
    
    def create_widgets(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="优雅中止恢复功能测试", 
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 说明文本
        info_text = """
此测试程序用于验证优雅中止恢复功能：

1. 启动策略GUI优雅版
2. 运行一些策略任务
3. 使用"优雅停止"功能暂停任务
4. 使用此程序查看和管理暂停状态
5. 测试恢复功能
        """
        
        info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Button(button_frame, text="🔄 刷新暂停状态", 
                  command=self.refresh_pause_states).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="🚀 启动策略GUI", 
                  command=self.launch_strategy_gui).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="🗑️ 清理所有暂停状态", 
                  command=self.cleanup_all_pause_states).pack(side=tk.LEFT, padx=(0, 10))
        
        # 暂停状态列表
        list_frame = ttk.LabelFrame(main_frame, text="暂停状态文件", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ('任务ID', '策略名称', '暂停时间', '已处理', '剩余', '状态文件')
        self.pause_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.pause_tree.heading(col, text=col)
            self.pause_tree.column(col, width=120, anchor='center')
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.pause_tree.yview)
        self.pause_tree.configure(yscrollcommand=scrollbar.set)
        
        self.pause_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右键菜单
        self.create_context_menu()
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="就绪")
        self.status_label.pack(pady=(10, 0))
    
    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="📄 查看详细信息", command=self.view_pause_details)
        self.context_menu.add_command(label="▶️ 模拟恢复", command=self.simulate_resume)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🗑️ 删除暂停状态", command=self.delete_pause_state)
        
        self.pause_tree.bind("<Button-3>", self.show_context_menu)
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        selection = self.pause_tree.selection()
        if selection:
            self.context_menu.post(event.x_root, event.y_root)
    
    def refresh_pause_states(self):
        """刷新暂停状态列表"""
        # 清空现有项目
        for item in self.pause_tree.get_children():
            self.pause_tree.delete(item)
        
        # 查找暂停状态文件
        import glob
        pause_files = glob.glob("temp_pause_state_*.json")
        
        if not pause_files:
            self.status_label.config(text="没有找到暂停状态文件")
            return
        
        count = 0
        for pause_file in pause_files:
            try:
                with open(pause_file, 'r', encoding='utf-8') as f:
                    pause_data = json.load(f)
                
                task_id = pause_data.get('task_id', 'Unknown')
                strategy_name = pause_data.get('strategy_name', 'Unknown')
                pause_time = pause_data.get('pause_time', 'Unknown')
                processed_count = len(pause_data.get('processed_files', []))
                remaining_count = len(pause_data.get('remaining_files', []))
                
                # 格式化暂停时间
                try:
                    dt = datetime.fromisoformat(pause_time)
                    formatted_time = dt.strftime("%m-%d %H:%M:%S")
                except:
                    formatted_time = pause_time
                
                self.pause_tree.insert('', 'end', values=(
                    task_id, strategy_name, formatted_time, 
                    processed_count, remaining_count, os.path.basename(pause_file)
                ))
                count += 1
                
            except Exception as e:
                print(f"读取暂停状态文件失败 {pause_file}: {str(e)}")
        
        self.status_label.config(text=f"找到 {count} 个暂停状态文件")
    
    def view_pause_details(self):
        """查看暂停状态详细信息"""
        selection = self.pause_tree.selection()
        if not selection:
            return
        
        item = selection[0]
        values = self.pause_tree.item(item)['values']
        task_id = values[0]
        
        pause_file = f"temp_pause_state_{task_id}.json"
        if not os.path.exists(pause_file):
            messagebox.showerror("错误", f"暂停状态文件不存在: {pause_file}")
            return
        
        try:
            with open(pause_file, 'r', encoding='utf-8') as f:
                pause_data = json.load(f)
            
            # 创建详细信息窗口
            detail_window = tk.Toplevel(self.root)
            detail_window.title(f"任务 {task_id} 详细信息")
            detail_window.geometry("600x400")
            
            # 创建文本框显示JSON内容
            text_widget = tk.Text(detail_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 格式化显示JSON
            formatted_json = json.dumps(pause_data, ensure_ascii=False, indent=2)
            text_widget.insert(tk.END, formatted_json)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("错误", f"读取暂停状态失败: {str(e)}")
    
    def simulate_resume(self):
        """模拟恢复任务"""
        selection = self.pause_tree.selection()
        if not selection:
            return
        
        item = selection[0]
        values = self.pause_tree.item(item)['values']
        task_id = values[0]
        
        # 确认模拟恢复
        confirm = messagebox.askyesno("确认模拟恢复", 
                                    f"确定要模拟恢复任务 {task_id} 吗？\n"
                                    f"这只是测试，不会实际运行策略。")
        if not confirm:
            return
        
        try:
            # 导入恢复函数进行测试
            from 策略工作进程_优雅版 import resume_strategy_worker_process
            
            self.status_label.config(text=f"正在模拟恢复任务 {task_id}...")
            
            # 在后台线程中运行，避免阻塞UI
            def run_resume():
                try:
                    result = resume_strategy_worker_process(task_id)
                    
                    # 在主线程中更新UI
                    self.root.after(0, lambda: self.on_resume_complete(task_id, result))
                    
                except Exception as e:
                    self.root.after(0, lambda: self.on_resume_error(task_id, str(e)))
            
            threading.Thread(target=run_resume, daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("错误", f"模拟恢复失败: {str(e)}")
            self.status_label.config(text="就绪")
    
    def on_resume_complete(self, task_id, result):
        """恢复完成回调"""
        if result['success']:
            messagebox.showinfo("恢复成功", f"任务 {task_id} 模拟恢复成功！")
        else:
            messagebox.showwarning("恢复失败", f"任务 {task_id} 模拟恢复失败:\n{result['error']}")
        
        self.status_label.config(text="就绪")
        self.refresh_pause_states()
    
    def on_resume_error(self, task_id, error):
        """恢复错误回调"""
        messagebox.showerror("恢复异常", f"任务 {task_id} 恢复过程中发生异常:\n{error}")
        self.status_label.config(text="就绪")
    
    def delete_pause_state(self):
        """删除暂停状态"""
        selection = self.pause_tree.selection()
        if not selection:
            return
        
        item = selection[0]
        values = self.pause_tree.item(item)['values']
        task_id = values[0]
        
        # 确认删除
        confirm = messagebox.askyesno("确认删除", 
                                    f"确定要删除任务 {task_id} 的暂停状态吗？\n"
                                    f"删除后将无法恢复该任务。")
        if not confirm:
            return
        
        try:
            pause_file = f"temp_pause_state_{task_id}.json"
            if os.path.exists(pause_file):
                os.remove(pause_file)
                messagebox.showinfo("删除成功", f"已删除任务 {task_id} 的暂停状态")
                self.refresh_pause_states()
            else:
                messagebox.showwarning("文件不存在", f"暂停状态文件不存在: {pause_file}")
        
        except Exception as e:
            messagebox.showerror("删除失败", f"删除暂停状态失败: {str(e)}")
    
    def cleanup_all_pause_states(self):
        """清理所有暂停状态"""
        import glob
        pause_files = glob.glob("temp_pause_state_*.json")
        
        if not pause_files:
            messagebox.showinfo("提示", "没有找到暂停状态文件")
            return
        
        # 确认清理
        confirm = messagebox.askyesno("确认清理", 
                                    f"找到 {len(pause_files)} 个暂停状态文件。\n"
                                    f"确定要清理所有暂停状态吗？")
        if not confirm:
            return
        
        try:
            count = 0
            for pause_file in pause_files:
                os.remove(pause_file)
                count += 1
            
            messagebox.showinfo("清理完成", f"已清理 {count} 个暂停状态文件")
            self.refresh_pause_states()
            
        except Exception as e:
            messagebox.showerror("清理失败", f"清理暂停状态失败: {str(e)}")
    
    def launch_strategy_gui(self):
        """启动策略GUI"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "策略GUI优雅版.py"])
            self.status_label.config(text="已启动策略GUI优雅版")
        except Exception as e:
            messagebox.showerror("启动失败", f"启动策略GUI失败: {str(e)}")


def main():
    """主函数"""
    root = tk.Tk()
    app = ResumeTestWindow(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序异常: {str(e)}")


if __name__ == "__main__":
    main()
