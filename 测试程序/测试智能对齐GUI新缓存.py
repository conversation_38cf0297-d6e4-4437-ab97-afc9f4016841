#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试智能对齐GUI对新缓存机制的支持
"""

import os
import sys
import glob

def test_new_cache_support():
    """测试新缓存机制支持"""
    print("测试智能对齐GUI的新缓存机制支持")
    print("=" * 60)
    
    # 检查缓存目录结构
    cache_dir = "../cache"
    if not os.path.exists(cache_dir):
        print("❌ 缓存目录不存在")
        return False
    
    print(f"✅ 缓存目录存在: {cache_dir}")
    
    # 检查新缓存机制目录
    strategy_dirs = []
    for item in os.listdir(cache_dir):
        item_path = os.path.join(cache_dir, item)
        if os.path.isdir(item_path):
            pkl_files = glob.glob(os.path.join(item_path, "*.pkl"))
            if pkl_files:
                strategy_dirs.append((item, len(pkl_files)))
    
    if strategy_dirs:
        print(f"✅ 发现新缓存机制策略目录: {len(strategy_dirs)} 个")
        for strategy_name, file_count in strategy_dirs:
            print(f"  - {strategy_name}: {file_count} 个股票缓存文件")
    else:
        print("⚠️ 未发现新缓存机制策略目录")
    
    # 检查旧缓存机制文件
    old_cache_files = glob.glob(os.path.join(cache_dir, "strategy_cache_*.pkl"))
    if old_cache_files:
        print(f"✅ 发现旧缓存机制文件: {len(old_cache_files)} 个")
        for cache_file in old_cache_files[:3]:  # 只显示前3个
            print(f"  - {os.path.basename(cache_file)}")
        if len(old_cache_files) > 3:
            print(f"  - ... 还有 {len(old_cache_files) - 3} 个文件")
    else:
        print("⚠️ 未发现旧缓存机制文件")
    
    return True

def test_gui_import():
    """测试GUI模块导入"""
    print("\n测试GUI模块导入")
    print("=" * 30)
    
    try:
        # 添加父目录到路径
        sys.path.insert(0, '..')
        
        from 多策略收益率对比_智能对齐GUI import MultiStrategyComparisonWindow
        print("✅ 成功导入智能对齐GUI模块")
        
        # 检查新方法是否存在
        methods_to_check = [
            'load_available_strategies',
            '_load_new_cache_strategies', 
            'load_new_cache_equity_curves_smart_align',
            'load_cached_equity_curves_smart_align'
        ]
        
        for method_name in methods_to_check:
            if hasattr(MultiStrategyComparisonWindow, method_name):
                print(f"✅ 方法存在: {method_name}")
            else:
                print(f"❌ 方法缺失: {method_name}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {str(e)}")
        return False

def test_strategy_gui_integration():
    """测试策略GUI集成"""
    print("\n测试策略GUI集成")
    print("=" * 30)
    
    try:
        gui_file = "../策略GUI优雅版.py"
        if not os.path.exists(gui_file):
            print(f"❌ GUI文件不存在: {gui_file}")
            return False
        
        with open(gui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查多策略对比功能
        if 'open_multi_strategy_comparison' in content:
            print("✅ 多策略对比功能存在")
            
            # 检查是否使用智能对齐GUI
            if '多策略收益率对比_智能对齐GUI' in content:
                print("✅ 使用智能对齐GUI版本")
            else:
                print("⚠️ 可能使用其他版本的多策略对比")
            
            # 检查是否移除了命令行工具调用
            if 'multi_strategy_compare.py' not in content:
                print("✅ 已移除命令行工具调用")
            else:
                print("⚠️ 仍包含命令行工具调用")
                
        else:
            print("❌ 多策略对比功能缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查GUI集成失败: {str(e)}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n使用指南")
    print("=" * 30)
    print("1. 启动策略GUI优雅版")
    print("2. 点击'多策略对比'按钮")
    print("3. 智能对齐GUI将自动检测并支持:")
    print("   - 新缓存机制（单个股票文件）")
    print("   - 旧缓存机制（策略汇总文件）")
    print("4. 选择要对比的策略")
    print("5. 设置参数并开始分析")
    print("\n新缓存机制优势:")
    print("- ✅ 避免重复计算已处理的股票")
    print("- ✅ 支持断点续传")
    print("- ✅ 更快的增量更新")
    print("- ✅ 更好的错误恢复能力")

if __name__ == "__main__":
    print("智能对齐GUI新缓存机制支持测试")
    print("=" * 80)
    
    # 运行测试
    cache_ok = test_new_cache_support()
    import_ok = test_gui_import()
    integration_ok = test_strategy_gui_integration()
    
    # 显示使用指南
    show_usage_guide()
    
    # 总结
    print("\n" + "=" * 80)
    if cache_ok and import_ok and integration_ok:
        print("✅ 所有测试通过！智能对齐GUI已支持新缓存机制")
    else:
        print("⚠️ 部分测试未通过，请检查相关问题")
    
    print("\n测试完成！") 