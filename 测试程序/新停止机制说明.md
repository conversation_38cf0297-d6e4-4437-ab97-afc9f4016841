# 新停止机制说明文档

## 概述

基于新的缓存机制，我们重新设计了优雅停止和强制停止的机制，使其更加精确和安全。

## 修改内容

### 1. 优雅停止机制改进

**旧机制：**
- 每10只股票检查一次停止信号
- 停止时可能丢失当前批次的数据

**新机制：**
- ✅ **等待当前股票完成**：收到优雅停止信号后，等待当前正在计算的股票完成
- ✅ **保存计算结果**：当前股票的计算结果会被保存到缓存
- ✅ **精确停止**：在股票处理完成后立即停止，不会继续处理下一只股票

### 2. 强制停止机制改进

**旧机制：**
- 直接终止进程，可能丢失大量数据

**新机制：**
- ✅ **立即停止**：收到强制停止信号后立即停止当前股票计算
- ✅ **无需保存**：不保存当前正在计算的股票结果
- ✅ **保留已完成**：已经完成并保存的股票结果会保留

### 3. 技术实现

#### 文件标志系统
```python
# 优雅停止标志文件
graceful_stop_flag_file = f"temp_graceful_stop_{task_id}.flag"

# 强制停止标志文件  
force_stop_flag_file = f"temp_force_stop_{task_id}.flag"
```

#### 检查函数
```python
def check_graceful_stop_signal():
    """检查优雅停止信号文件"""
    return os.path.exists(graceful_stop_flag_file)

def check_force_stop_signal():
    """检查强制停止信号文件"""
    return os.path.exists(force_stop_flag_file)
```

#### 停止逻辑
```python
for i, file_path in enumerate(files_to_process, 1):
    # 1. 优先检查强制停止（立即停止）
    if check_force_stop_signal():
        print("🛑 收到强制停止信号，立即停止")
        return
    
    # 2. 检查优雅停止（标记但继续处理当前股票）
    graceful_stop_requested = check_graceful_stop_signal()
    if graceful_stop_requested:
        print("⏸️ 收到优雅停止信号，等待当前股票处理完成")
    
    # 3. 处理当前股票
    stock_code = process_stock(file_path)
    
    # 4. 处理完成后检查优雅停止
    if graceful_stop_requested:
        print(f"✅ {stock_code} 处理完成，响应优雅停止信号")
        return
```

## 界面更新

### 按钮文字更新
- **优雅停止** → **⏸️ 优雅停止（等待完成）**
- **强制停止** → **🛑 强制停止（立即停止）**

### 确认对话框更新
- **优雅停止**：说明会等待当前股票处理完成，已处理结果会保存
- **强制停止**：说明会立即停止，正在计算的股票不保存，已完成的保留

### 日志信息更新
- 更详细的停止过程说明
- 明确区分两种停止方式的行为

## 使用场景

### 优雅停止适用场景
- ✅ 需要保存当前进度
- ✅ 不急于立即停止
- ✅ 希望最大化数据完整性
- ✅ 正常的计划停止

### 强制停止适用场景
- ✅ 需要立即停止（如系统资源不足）
- ✅ 当前股票计算出现问题
- ✅ 紧急情况下的快速停止
- ✅ 优雅停止无响应时的备选方案

## 优势对比

| 方面 | 旧机制 | 新机制 |
|------|--------|--------|
| **停止精度** | 每10只股票检查 | 每只股票检查 |
| **数据安全** | 可能丢失批次数据 | 最小化数据丢失 |
| **用户控制** | 只有一种停止方式 | 两种停止方式可选 |
| **响应速度** | 较慢（最多等10只股票） | 快速（最多等1只股票） |
| **缓存机制** | 批量保存，风险较高 | 实时保存，风险最低 |

## 测试验证

运行 `测试新停止机制.py` 可以验证：
- ✅ 文件标志创建和清理
- ✅ 停止信号检查逻辑
- ✅ 优雅停止等待机制
- ✅ 强制停止立即响应

## 注意事项

1. **文件清理**：系统会自动清理停止标志文件，避免残留
2. **并发安全**：每个任务使用独立的标志文件，避免冲突
3. **超时处理**：优雅停止仍有超时机制，超时后可选择强制停止
4. **兼容性**：新机制向后兼容，不影响现有功能

## 总结

新的停止机制提供了更精确、更安全的任务控制方式：

- **优雅停止**：等待当前股票完成 → 保存结果 → 停止
- **强制停止**：立即停止 → 不保存当前股票 → 保留已完成

这样的设计既保证了数据的完整性，又提供了灵活的控制选项，满足不同场景下的停止需求。
