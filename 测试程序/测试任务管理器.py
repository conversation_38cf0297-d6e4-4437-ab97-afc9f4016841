#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试任务管理器功能
验证任务配置文件的创建、保存、加载等功能
"""

import sys
import os
import json
from datetime import datetime

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from 任务管理器 import task_manager


def test_task_creation():
    """测试任务创建"""
    print("🧪 测试任务创建...")
    
    # 创建测试任务参数
    test_params = {
        'type': 'preset',
        'name': '稳健策略',
        'short_window': 5,
        'long_window': 20,
        'data_dir': '../日线数据',
        'execution_price': 'open',
        'use_enhanced_logic': True,
        'use_cache': True,
        'generate_charts': False,
        'save_interval': 20
    }
    
    # 创建任务
    task_id = task_manager.create_task(test_params)
    print(f"✅ 创建任务成功，任务ID: {task_id}")
    
    # 验证任务配置文件是否存在
    config_file = os.path.join(task_manager.tasks_dir, f"task_{task_id}.json")
    if os.path.exists(config_file):
        print(f"✅ 任务配置文件已创建: {config_file}")
        
        # 读取并显示配置内容
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("📄 任务配置内容:")
        print(json.dumps(config, ensure_ascii=False, indent=2))
    else:
        print(f"❌ 任务配置文件未找到: {config_file}")
    
    return task_id


def test_task_status_update(task_id):
    """测试任务状态更新"""
    print(f"\n🧪 测试任务状态更新 (任务ID: {task_id})...")
    
    # 更新任务状态为运行中
    task_manager.update_task_status(task_id, 'running')
    print("✅ 更新任务状态为运行中")
    
    # 更新任务进度
    task_manager.update_task_progress(task_id, total_stocks=1000, processed_stocks=250, successful_stocks=200)
    print("✅ 更新任务进度: 250/1000 (25%)")
    
    # 添加执行日志
    task_manager.add_execution_log(task_id, "开始处理股票数据", 'info')
    task_manager.add_execution_log(task_id, "处理了250只股票", 'info')
    task_manager.add_execution_log(task_id, "发现一些数据异常", 'warning')
    print("✅ 添加执行日志")
    
    # 更新结果
    task_manager.update_task_status(task_id, 'running', 
                                  **{'results.avg_return': 0.15,
                                     'results.win_rate': 0.65,
                                     'results.max_return': 0.45,
                                     'results.min_return': -0.20})
    print("✅ 更新任务结果")


def test_task_loading(task_id):
    """测试任务加载"""
    print(f"\n🧪 测试任务加载 (任务ID: {task_id})...")
    
    # 加载任务配置
    config = task_manager.load_task_config(task_id)
    
    if config:
        print("✅ 任务配置加载成功")
        print(f"   任务状态: {config.get('status')}")
        print(f"   创建时间: {config.get('created_time')}")
        print(f"   更新时间: {config.get('updated_time')}")
        
        progress = config.get('progress', {})
        print(f"   进度: {progress.get('processed_stocks', 0)}/{progress.get('total_stocks', 0)} ({progress.get('progress_percentage', 0):.1f}%)")
        
        results = config.get('results', {})
        print(f"   平均收益率: {results.get('avg_return', 0):.2%}")
        print(f"   胜率: {results.get('win_rate', 0):.1%}")
        
        logs = config.get('execution_log', [])
        print(f"   执行日志条数: {len(logs)}")
        
        if logs:
            print("   最新日志:")
            for log in logs[-3:]:  # 显示最新3条日志
                print(f"     [{log.get('timestamp', '')}] {log.get('level', 'info').upper()}: {log.get('message', '')}")
    else:
        print("❌ 任务配置加载失败")


def test_all_tasks_listing():
    """测试获取所有任务"""
    print(f"\n🧪 测试获取所有任务...")
    
    all_tasks = task_manager.get_all_tasks()
    print(f"✅ 获取到 {len(all_tasks)} 个任务")
    
    for task in all_tasks:
        task_id = task.get('task_id', 'Unknown')
        status = task.get('status', 'unknown')
        created_time = task.get('created_time', '')
        
        # 格式化时间
        try:
            dt = datetime.fromisoformat(created_time)
            formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            formatted_time = created_time
        
        print(f"   任务 {task_id}: {status} (创建于 {formatted_time})")


def test_task_completion(task_id):
    """测试任务完成"""
    print(f"\n🧪 测试任务完成 (任务ID: {task_id})...")
    
    # 更新最终进度
    task_manager.update_task_progress(task_id, processed_stocks=1000, successful_stocks=850)
    
    # 更新最终结果
    task_manager.update_task_status(task_id, 'completed',
                                  **{'results.avg_return': 0.18,
                                     'results.win_rate': 0.68,
                                     'results.max_return': 0.52,
                                     'results.min_return': -0.18,
                                     'results.total_trades': 850})
    
    # 添加完成日志
    task_manager.add_execution_log(task_id, "任务执行完成，处理了1000只股票", 'info')
    
    print("✅ 任务标记为完成")


def test_task_cleanup():
    """测试任务清理"""
    print(f"\n🧪 测试任务清理...")
    
    # 创建一个测试任务用于删除
    test_params = {
        'type': 'custom',
        'short_window': 3,
        'long_window': 10,
        'data_dir': '../日线数据',
        'execution_price': 'close'
    }
    
    delete_task_id = task_manager.create_task(test_params)
    print(f"✅ 创建测试删除任务: {delete_task_id}")
    
    # 标记为失败状态
    task_manager.update_task_status(delete_task_id, 'failed')
    task_manager.add_execution_log(delete_task_id, "测试失败任务", 'error')
    
    # 删除任务
    if task_manager.delete_task(delete_task_id):
        print(f"✅ 删除任务成功: {delete_task_id}")
    else:
        print(f"❌ 删除任务失败: {delete_task_id}")


def main():
    """主测试函数"""
    print("🚀 开始测试任务管理器功能")
    print("=" * 50)
    
    try:
        # 测试任务创建
        task_id = test_task_creation()
        
        # 测试任务状态更新
        test_task_status_update(task_id)
        
        # 测试任务加载
        test_task_loading(task_id)
        
        # 测试获取所有任务
        test_all_tasks_listing()
        
        # 测试任务完成
        test_task_completion(task_id)
        
        # 再次加载查看最终状态
        test_task_loading(task_id)
        
        # 测试任务清理
        test_task_cleanup()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！")
        
        # 显示任务目录信息
        tasks_dir = task_manager.tasks_dir
        if os.path.exists(tasks_dir):
            task_files = [f for f in os.listdir(tasks_dir) if f.startswith('task_') and f.endswith('.json')]
            print(f"📁 任务目录: {tasks_dir}")
            print(f"📄 任务文件数量: {len(task_files)}")
            
            if task_files:
                print("📋 任务文件列表:")
                for task_file in task_files:
                    print(f"   {task_file}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
