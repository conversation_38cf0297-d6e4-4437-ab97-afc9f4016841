#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试GUI中的新缓存机制
"""

import os
import glob
import time
from datetime import datetime

def test_gui_cache_mechanism():
    """测试GUI中的新缓存机制"""
    print("🧪 测试GUI中的新缓存机制")
    print("=" * 60)
    
    # 检查缓存目录结构
    cache_base_dir = "./cache"
    if os.path.exists(cache_base_dir):
        print(f"📁 缓存基础目录存在: {cache_base_dir}")
        
        # 列出所有策略缓存目录
        strategy_dirs = [d for d in os.listdir(cache_base_dir) 
                        if os.path.isdir(os.path.join(cache_base_dir, d))]
        
        if strategy_dirs:
            print(f"📊 发现 {len(strategy_dirs)} 个策略缓存目录:")
            for strategy_dir in strategy_dirs:
                strategy_path = os.path.join(cache_base_dir, strategy_dir)
                cache_files = glob.glob(os.path.join(strategy_path, "*.pkl"))
                print(f"  • {strategy_dir}: {len(cache_files)} 个股票缓存文件")
                
                # 显示前几个文件的详情
                if cache_files:
                    for i, cache_file in enumerate(cache_files[:3]):
                        stock_code = os.path.basename(cache_file).replace('.pkl', '')
                        file_size = os.path.getsize(cache_file) / 1024  # KB
                        create_time = datetime.fromtimestamp(os.path.getctime(cache_file))
                        print(f"    - {stock_code}: {file_size:.1f}KB, {create_time.strftime('%Y-%m-%d %H:%M')}")
                    
                    if len(cache_files) > 3:
                        print(f"    ... 还有 {len(cache_files) - 3} 个文件")
        else:
            print("📝 未发现策略缓存目录")
    else:
        print(f"📝 缓存基础目录不存在: {cache_base_dir}")
    
    print("\n" + "=" * 60)
    
    # 检查旧缓存文件
    old_cache_files = glob.glob(os.path.join(cache_base_dir, "strategy_cache_*.pkl"))
    if old_cache_files:
        print(f"⚠️ 发现 {len(old_cache_files)} 个旧格式缓存文件:")
        for old_file in old_cache_files:
            file_name = os.path.basename(old_file)
            file_size = os.path.getsize(old_file) / 1024 / 1024  # MB
            create_time = datetime.fromtimestamp(os.path.getctime(old_file))
            print(f"  • {file_name}: {file_size:.1f}MB, {create_time.strftime('%Y-%m-%d %H:%M')}")
        print("💡 建议运行策略时会自动迁移到新格式")
    else:
        print("✅ 未发现旧格式缓存文件")
    
    print("\n" + "=" * 60)
    
    # 测试策略工作进程的新缓存机制
    print("🧪 测试策略工作进程的新缓存机制")
    
    try:
        from 策略工作进程_优雅版 import strategy_worker_process_graceful
        
        # 创建测试参数
        test_params = {
            'task_id': 'test_cache',
            'name': 'MA5_20_测试',
            'short_window': 5,
            'long_window': 20,
            'data_dir': '../日线数据',
            'execution_price': 'open',
            'use_enhanced_logic': True,
            'use_cache': True,
            'generate_charts': False,
            'save_interval': 5
        }
        
        print(f"📊 测试参数: {test_params['name']}")
        print(f"📁 数据目录: {test_params['data_dir']}")
        
        # 检查数据目录
        if os.path.exists(test_params['data_dir']):
            stock_files = glob.glob(os.path.join(test_params['data_dir'], "*.txt"))
            print(f"📈 发现 {len(stock_files)} 个股票数据文件")
            
            if len(stock_files) > 0:
                print("✅ 数据目录检查通过，可以进行缓存测试")
                
                # 这里可以添加实际的工作进程测试
                # 但为了避免长时间运行，我们只做检查
                print("💡 如需完整测试，请运行GUI并添加任务到队列")
            else:
                print("❌ 数据目录为空，无法进行测试")
        else:
            print(f"❌ 数据目录不存在: {test_params['data_dir']}")
            
    except ImportError as e:
        print(f"❌ 导入策略工作进程失败: {str(e)}")
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print("1. 新缓存机制将为每个策略参数组合创建独立目录")
    print("2. 每个股票的计算结果会立即保存为单独的.pkl文件")
    print("3. 支持断点续传，提高运行效率")
    print("4. 旧缓存文件会自动迁移到新格式")
    print("5. GUI中的工作进程已更新为使用新缓存机制")

def check_cache_compatibility():
    """检查缓存兼容性"""
    print("\n🔍 检查缓存兼容性")
    print("-" * 40)
    
    try:
        from 双均线策略_增强版 import MovingAverageStrategyEnhanced
        
        # 创建策略实例测试
        strategy = MovingAverageStrategyEnhanced(
            short_window=5,
            long_window=20,
            data_dir="../日线数据",
            execution_price='open'
        )
        
        print(f"✅ 策略实例创建成功")
        print(f"📁 缓存目录: {strategy.cache_dir}")
        print(f"📄 旧缓存文件: {strategy.legacy_cache_file}")
        
        # 检查新缓存机制方法
        methods_to_check = [
            'save_single_stock_cache',
            'load_single_stock_cache', 
            'load_individual_cache',
            'migrate_legacy_cache'
        ]
        
        for method_name in methods_to_check:
            if hasattr(strategy, method_name):
                print(f"✅ 方法存在: {method_name}")
            else:
                print(f"❌ 方法缺失: {method_name}")
        
        print("✅ 缓存兼容性检查完成")
        
    except Exception as e:
        print(f"❌ 缓存兼容性检查失败: {str(e)}")

if __name__ == "__main__":
    test_gui_cache_mechanism()
    check_cache_compatibility()
    
    print(f"\n🎉 测试完成！")
    print(f"📝 如需完整测试新缓存机制，请运行策略GUI优雅版.py") 