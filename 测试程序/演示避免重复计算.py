#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
演示新缓存机制如何避免重复计算
"""

import os
import glob
import time
from datetime import datetime
from 双均线策略_增强版 import MovingAverageStrategyEnhanced

def demonstrate_cache_mechanism():
    """演示缓存机制避免重复计算"""
    print("🎯 演示新缓存机制 - 避免重复计算")
    print("=" * 80)
    
    # 创建策略实例
    strategy = MovingAverageStrategyEnhanced(
        short_window=5,
        long_window=20,
        data_dir="../日线数据",
        execution_price='open'
    )
    
    print(f"📊 策略参数: MA{strategy.short_window}-{strategy.long_window}, 执行价格: {strategy.execution_price}")
    print(f"📁 缓存目录: {strategy.cache_dir}")
    
    # 检查缓存目录状态
    if os.path.exists(strategy.cache_dir):
        existing_cache_files = glob.glob(os.path.join(strategy.cache_dir, "*.pkl"))
        print(f"📦 发现 {len(existing_cache_files)} 个现有缓存文件")
    else:
        print("📝 缓存目录不存在，将创建新目录")
        existing_cache_files = []
    
    # 获取所有股票文件
    stock_files = glob.glob(os.path.join(strategy.data_dir, "*.txt"))
    print(f"📈 数据目录中共有 {len(stock_files)} 只股票")
    
    if not stock_files:
        print("❌ 未找到股票数据文件")
        return
    
    print("\n" + "=" * 80)
    print("🧪 第一次运行 - 演示初始计算")
    print("=" * 80)
    
    # 第一次运行（只处理前10只股票进行演示）
    print("📊 限制处理前10只股票进行演示...")
    
    # 临时修改数据目录，只包含前10只股票
    demo_dir = "./demo_data"
    os.makedirs(demo_dir, exist_ok=True)
    
    # 复制前10只股票文件到演示目录
    demo_files = []
    for i, stock_file in enumerate(stock_files[:10]):
        stock_code = os.path.basename(stock_file).replace('.txt', '')
        demo_file = os.path.join(demo_dir, f"{stock_code}.txt")
        
        # 只复制文件头部分（减少文件大小，加快演示）
        try:
            with open(stock_file, 'r', encoding='utf-8') as src:
                lines = src.readlines()[:100]  # 只取前100行
            
            with open(demo_file, 'w', encoding='utf-8') as dst:
                dst.writelines(lines)
            
            demo_files.append(demo_file)
            print(f"  📄 准备演示文件: {stock_code}")
        except Exception as e:
            print(f"  ❌ 准备文件失败 {stock_code}: {str(e)}")
    
    if not demo_files:
        print("❌ 无法准备演示文件")
        return
    
    # 创建演示策略实例
    demo_strategy = MovingAverageStrategyEnhanced(
        short_window=5,
        long_window=20,
        data_dir=demo_dir,
        execution_price='open'
    )
    
    print(f"\n🚀 开始第一次运行（处理 {len(demo_files)} 只股票）...")
    start_time = time.time()
    
    # 第一次运行
    demo_strategy.run_strategy(generate_charts=False, use_cache=True, save_interval=3)
    
    first_run_time = time.time() - start_time
    first_run_results = len(demo_strategy.results)
    
    print(f"\n📊 第一次运行结果:")
    print(f"   ⏱️ 耗时: {first_run_time:.2f} 秒")
    print(f"   📈 成功处理: {first_run_results} 只股票")
    
    # 检查缓存文件
    cache_files = glob.glob(os.path.join(demo_strategy.cache_dir, "*.pkl"))
    print(f"   💾 生成缓存文件: {len(cache_files)} 个")
    
    print("\n" + "=" * 80)
    print("🔄 第二次运行 - 演示缓存加载（避免重复计算）")
    print("=" * 80)
    
    # 创建新的策略实例（模拟重新启动程序）
    demo_strategy2 = MovingAverageStrategyEnhanced(
        short_window=5,
        long_window=20,
        data_dir=demo_dir,
        execution_price='open'
    )
    
    print(f"🚀 开始第二次运行（相同参数）...")
    start_time = time.time()
    
    # 第二次运行
    demo_strategy2.run_strategy(generate_charts=False, use_cache=True, save_interval=3)
    
    second_run_time = time.time() - start_time
    second_run_results = len(demo_strategy2.results)
    
    print(f"\n📊 第二次运行结果:")
    print(f"   ⏱️ 耗时: {second_run_time:.2f} 秒")
    print(f"   📈 加载结果: {second_run_results} 只股票")
    print(f"   🚀 速度提升: {first_run_time / second_run_time:.2f}x")
    
    print("\n" + "=" * 80)
    print("🔧 第三次运行 - 演示参数变更时的重新计算")
    print("=" * 80)
    
    # 创建不同参数的策略实例
    demo_strategy3 = MovingAverageStrategyEnhanced(
        short_window=10,  # 改变参数
        long_window=30,   # 改变参数
        data_dir=demo_dir,
        execution_price='open'
    )
    
    print(f"🚀 开始第三次运行（不同参数: MA10-30）...")
    start_time = time.time()
    
    # 第三次运行（不同参数）
    demo_strategy3.run_strategy(generate_charts=False, use_cache=True, save_interval=3)
    
    third_run_time = time.time() - start_time
    third_run_results = len(demo_strategy3.results)
    
    print(f"\n📊 第三次运行结果:")
    print(f"   ⏱️ 耗时: {third_run_time:.2f} 秒")
    print(f"   📈 重新计算: {third_run_results} 只股票")
    print(f"   💾 新缓存目录: {demo_strategy3.cache_dir}")
    
    # 检查新的缓存目录
    new_cache_files = glob.glob(os.path.join(demo_strategy3.cache_dir, "*.pkl"))
    print(f"   💾 新缓存文件: {len(new_cache_files)} 个")
    
    print("\n" + "=" * 80)
    print("📋 缓存机制总结")
    print("=" * 80)
    
    print("✅ 新缓存机制的优势:")
    print("   1. 🚫 避免重复计算：相同参数的股票只计算一次")
    print("   2. 📁 独立目录：不同策略参数使用独立缓存目录")
    print("   3. 💾 实时保存：每个股票计算完成后立即保存")
    print("   4. 🔍 智能检测：自动检测参数变更和文件变化")
    print("   5. ⚡ 显著提速：缓存命中时速度提升数倍")
    
    print(f"\n📊 本次演示数据:")
    print(f"   第一次运行（全新计算）: {first_run_time:.2f}秒")
    print(f"   第二次运行（缓存加载）: {second_run_time:.2f}秒")
    print(f"   第三次运行（参数变更）: {third_run_time:.2f}秒")
    print(f"   缓存加速比: {first_run_time / second_run_time:.2f}x")
    
    # 清理演示文件
    print(f"\n🧹 清理演示文件...")
    try:
        import shutil
        if os.path.exists(demo_dir):
            shutil.rmtree(demo_dir)
        print("✅ 演示文件清理完成")
    except Exception as e:
        print(f"⚠️ 清理演示文件时出错: {str(e)}")

def show_cache_structure():
    """显示缓存目录结构"""
    print("\n" + "=" * 80)
    print("📁 当前缓存目录结构")
    print("=" * 80)
    
    cache_base_dir = "./cache"
    if not os.path.exists(cache_base_dir):
        print("📝 缓存目录不存在")
        return
    
    # 遍历缓存目录
    for item in os.listdir(cache_base_dir):
        item_path = os.path.join(cache_base_dir, item)
        
        if os.path.isdir(item_path):
            # 策略目录
            cache_files = glob.glob(os.path.join(item_path, "*.pkl"))
            total_size = sum(os.path.getsize(f) for f in cache_files) / 1024 / 1024  # MB
            print(f"📂 {item}/")
            print(f"   📊 股票缓存文件: {len(cache_files)} 个")
            print(f"   💾 总大小: {total_size:.2f} MB")
            
            # 显示前几个文件
            for i, cache_file in enumerate(cache_files[:3]):
                stock_code = os.path.basename(cache_file).replace('.pkl', '')
                file_size = os.path.getsize(cache_file) / 1024  # KB
                create_time = datetime.fromtimestamp(os.path.getctime(cache_file))
                print(f"   📄 {stock_code}: {file_size:.1f}KB, {create_time.strftime('%m-%d %H:%M')}")
            
            if len(cache_files) > 3:
                print(f"   📄 ... 还有 {len(cache_files) - 3} 个文件")
            print()
        
        elif item.endswith('.pkl'):
            # 旧格式缓存文件
            file_size = os.path.getsize(item_path) / 1024 / 1024  # MB
            create_time = datetime.fromtimestamp(os.path.getctime(item_path))
            print(f"📄 {item} (旧格式)")
            print(f"   💾 大小: {file_size:.2f} MB")
            print(f"   🕒 创建时间: {create_time.strftime('%Y-%m-%d %H:%M')}")
            print()

if __name__ == "__main__":
    print("🎯 新缓存机制演示 - 避免重复计算")
    print("=" * 80)
    print("本演示将展示新缓存机制如何智能地避免重复计算已处理的股票")
    print()
    
    # 显示当前缓存结构
    show_cache_structure()
    
    # 运行演示
    demonstrate_cache_mechanism()
    
    print(f"\n🎉 演示完成！")
    print("💡 在实际使用中，新缓存机制会:")
    print("   • 自动跳过已计算的股票")
    print("   • 只计算新增或变更的股票")
    print("   • 大幅提升重复运行的速度")
    print("   • 支持断点续传功能") 