#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多策略对比工具 - 支持新缓存机制（修复Unicode编码问题）
"""

import os
import json
import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 导入策略类
from 双均线策略_增强版 import MovingAverageStrategyEnhanced

def run_strategy(config, start_date=None, end_date=None, initial_capital=1000000):
    """
    运行单个策略配置
    
    Args:
        config: 策略配置字典
        start_date: 开始日期（暂未使用）
        end_date: 结束日期（暂未使用）
        initial_capital: 初始资金（暂未使用）
    
    Returns:
        dict: 策略结果
    """
    try:
        print(f"\n运行策略: {config['name']}")
        
        # 创建策略实例
        strategy = MovingAverageStrategyEnhanced(
            short_window=config['short_window'],
            long_window=config['long_window'],
            data_dir=config['data_dir'],
            execution_price=config.get('execution_price', 'open')
        )
        
        # 运行策略
        strategy.run_strategy(generate_charts=False, use_cache=True, save_interval=50)
        
        if not strategy.results:
            print(f"策略 {config['name']} 无有效结果")
            return None
        
        # 计算策略统计指标
        returns = [result['total_return'] for result in strategy.results.values()]
        
        if not returns:
            print(f"策略 {config['name']} 无有效收益数据")
            return None
        
        # 基本统计
        total_return = np.mean(returns)
        win_rate = len([r for r in returns if r > 0]) / len(returns)
        max_return = max(returns)
        min_return = min(returns)
        std_return = np.std(returns)
        
        # 计算年化收益率（假设持有期为1年）
        annual_return = total_return
        
        # 计算夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        if std_return > 0:
            sharpe_ratio = (annual_return - risk_free_rate) / std_return
        else:
            sharpe_ratio = 0
        
        # 计算最大回撤（简化计算）
        max_drawdown = abs(min_return) if min_return < 0 else 0
        
        # 计算盈亏比
        positive_returns = [r for r in returns if r > 0]
        negative_returns = [r for r in returns if r < 0]
        
        if positive_returns and negative_returns:
            avg_profit = np.mean(positive_returns)
            avg_loss = abs(np.mean(negative_returns))
            profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else 0
        else:
            profit_loss_ratio = 0
        
        # 创建权益曲线（简化版）
        cumulative_returns = np.cumsum(returns)
        equity_curve = [initial_capital * (1 + cr) for cr in cumulative_returns]
        
        # 计算回撤序列
        peak = initial_capital
        drawdown = []
        for value in equity_curve:
            if value > peak:
                peak = value
            dd = (value - peak) / peak
            drawdown.append(dd)
        
        result = {
            'strategy_name': config['name'],
            'total_return': total_return,
            'annual_return': annual_return,
            'win_rate': win_rate,
            'max_return': max_return,
            'min_return': min_return,
            'std_return': std_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'profit_loss_ratio': profit_loss_ratio,
            'stock_count': len(returns),
            'successful_backtests': len(strategy.results),
            'total_stocks': len(glob.glob(os.path.join(config['data_dir'], "*.txt"))),
            'equity_curve': equity_curve,
            'drawdown': drawdown,
            'returns': returns
        }
        
        print(f"策略 {config['name']} 运行完成")
        print(f"  平均收益率: {total_return:.2%}")
        print(f"  胜率: {win_rate:.2%}")
        print(f"  夏普比率: {sharpe_ratio:.2f}")
        print(f"  股票数量: {len(returns)}")
        
        return result
        
    except Exception as e:
        print(f"策略 {config['name']} 运行失败: {str(e)}")
        return None

def run_multi_strategy_compare(strategy_configs, start_date=None, end_date=None, initial_capital=1000000):
    """
    运行多策略对比
    
    Args:
        strategy_configs: 策略配置列表
        start_date: 开始日期
        end_date: 结束日期
        initial_capital: 初始资金
    """
    print("开始多策略对比分析")
    print("=" * 60)
    
    results = []
    
    for i, config in enumerate(strategy_configs, 1):
        print(f"\n进度: {i}/{len(strategy_configs)}")
        result = run_strategy(config, start_date, end_date, initial_capital)
        if result:
            results.append(result)
    
    if not results:
        print("没有可比较的策略结果")
        return
    
    print(f"\n成功运行 {len(results)} 个策略")
    
    # 创建charts目录
    os.makedirs('charts', exist_ok=True)
    
    # 生成对比图表
    create_comparison_charts(results)
    
    # 显示对比摘要
    display_comparison_summary(results)

def create_comparison_charts(results):
    """
    创建策略对比图表
    
    Args:
        results: 策略结果列表
    """
    print("\n生成对比图表...")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 1. 绘制权益曲线对比
    plt.figure(figsize=(15, 8))
    for result in results:
        strategy_name = result['strategy_name']
        plt.plot(result['equity_curve'], label=strategy_name, linewidth=2)
    
    plt.title('策略权益曲线对比', fontsize=16, fontweight='bold')
    plt.xlabel('时间序列', fontsize=12)
    plt.ylabel('账户价值', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('charts/equity_curve_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 绘制回撤对比
    plt.figure(figsize=(15, 8))
    for result in results:
        strategy_name = result['strategy_name']
        plt.plot(result['drawdown'], label=strategy_name, linewidth=2)
    
    plt.title('策略回撤对比', fontsize=16, fontweight='bold')
    plt.xlabel('时间序列', fontsize=12)
    plt.ylabel('回撤比例', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('charts/drawdown_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 创建性能指标对比表格
    performance_metrics = []
    for result in results:
        metrics = {
            '策略名称': result['strategy_name'],
            '平均收益率': f"{result['total_return']:.2%}",
            '年化收益率': f"{result['annual_return']:.2%}",
            '最大回撤': f"{result['max_drawdown']:.2%}",
            '夏普比率': f"{result['sharpe_ratio']:.2f}",
            '胜率': f"{result['win_rate']:.2%}",
            '盈亏比': f"{result['profit_loss_ratio']:.2f}",
            '股票数量': result['stock_count'],
            '成功回测': result['successful_backtests'],
            '总股票数': result['total_stocks']
        }
        performance_metrics.append(metrics)
    
    # 保存性能指标到CSV
    df_metrics = pd.DataFrame(performance_metrics)
    df_metrics.to_csv('charts/performance_metrics.csv', index=False, encoding='utf-8-sig')
    
    # 4. 创建性能指标对比柱状图
    plt.figure(figsize=(16, 10))
    
    # 选择要显示的指标
    numeric_metrics = ['total_return', 'annual_return', 'max_drawdown', 'sharpe_ratio', 'win_rate', 'profit_loss_ratio']
    metric_names = ['平均收益率', '年化收益率', '最大回撤', '夏普比率', '胜率', '盈亏比']
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    for i, (metric, name) in enumerate(zip(numeric_metrics, metric_names)):
        ax = axes[i]
        
        strategy_names = [r['strategy_name'] for r in results]
        values = [r[metric] for r in results]
        
        bars = ax.bar(range(len(strategy_names)), values, alpha=0.7)
        ax.set_title(name, fontsize=12, fontweight='bold')
        ax.set_xticks(range(len(strategy_names)))
        ax.set_xticklabels(strategy_names, rotation=45, ha='right')
        ax.grid(True, alpha=0.3)
        
        # 在柱子上显示数值
        for bar, value in zip(bars, values):
            height = bar.get_height()
            if metric in ['total_return', 'annual_return', 'max_drawdown', 'win_rate']:
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.1%}', ha='center', va='bottom', fontsize=9)
            else:
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.2f}', ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    plt.savefig('charts/performance_metrics_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("图表生成完成，保存在 charts/ 目录")

def display_comparison_summary(results):
    """
    显示对比结果摘要
    
    Args:
        results: 策略结果列表
    """
    print("\n策略对比结果摘要")
    print("=" * 80)
    
    # 按平均收益率排序
    sorted_results = sorted(results, key=lambda x: x['total_return'], reverse=True)
    
    print(f"{'排名':<4} {'策略名称':<20} {'平均收益率':<10} {'胜率':<8} {'夏普比率':<8} {'股票数量':<8}")
    print("-" * 80)
    
    for i, result in enumerate(sorted_results, 1):
        print(f"{i:<4} {result['strategy_name']:<20} "
              f"{result['total_return']:>8.2%} "
              f"{result['win_rate']:>6.2%} "
              f"{result['sharpe_ratio']:>6.2f} "
              f"{result['stock_count']:>6}")
    
    print("\n最佳策略:")
    best_strategy = sorted_results[0]
    print(f"   策略名称: {best_strategy['strategy_name']}")
    print(f"   平均收益率: {best_strategy['total_return']:.2%}")
    print(f"   年化收益率: {best_strategy['annual_return']:.2%}")
    print(f"   胜率: {best_strategy['win_rate']:.2%}")
    print(f"   夏普比率: {best_strategy['sharpe_ratio']:.2f}")

def create_default_strategy_configs():
    """
    创建默认的策略配置
    
    Returns:
        list: 策略配置列表
    """
    configs = [
        {
            "name": "MA5_20_开盘价",
            "short_window": 5,
            "long_window": 20,
            "execution_price": "open",
            "data_dir": "../日线数据"
        },
        {
            "name": "MA5_20_收盘价",
            "short_window": 5,
            "long_window": 20,
            "execution_price": "close",
            "data_dir": "../日线数据"
        },
        {
            "name": "MA10_30_开盘价",
            "short_window": 10,
            "long_window": 30,
            "execution_price": "open",
            "data_dir": "../日线数据"
        },
        {
            "name": "MA10_30_收盘价",
            "short_window": 10,
            "long_window": 30,
            "execution_price": "close",
            "data_dir": "../日线数据"
        },
        {
            "name": "MA5_30_开盘价",
            "short_window": 5,
            "long_window": 30,
            "execution_price": "open",
            "data_dir": "../日线数据"
        }
    ]
    return configs

def main():
    """主函数"""
    print("多策略对比工具 - 新缓存机制版")
    print("=" * 60)
    
    # 尝试加载策略配置文件
    config_file = 'strategy_config.json'
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                strategy_configs = json.load(f)
            print(f"已加载策略配置文件: {config_file}")
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            print("使用默认配置")
            strategy_configs = create_default_strategy_configs()
    else:
        print(f"配置文件不存在，创建默认配置: {config_file}")
        strategy_configs = create_default_strategy_configs()
        
        # 保存默认配置到文件
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(strategy_configs, f, ensure_ascii=False, indent=2)
            print(f"默认配置已保存到: {config_file}")
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")
    
    print(f"将对比 {len(strategy_configs)} 个策略配置")
    
    # 设置回测参数
    start_date = '2020-01-01'
    end_date = '2023-12-31'
    initial_capital = 1000000
    
    # 运行多策略对比
    run_multi_strategy_compare(strategy_configs, start_date, end_date, initial_capital)
    
    print(f"\n多策略对比完成！")
    print(f"图表保存在: charts/ 目录")
    print(f"详细数据保存在: charts/performance_metrics.csv")

if __name__ == '__main__':
    main() 