# 持久化任务管理功能说明

## 功能概述

持久化任务管理功能为每个计算任务创建独立的设置文件，记录任务的关键参数、执行进度和结果，支持跨会话的任务恢复和管理。

## 🎯 核心特性

### 1. **任务配置文件系统**
- **独立存储**：每个任务在 `tasks/` 目录下有独立的 JSON 配置文件
- **完整记录**：包含任务参数、进度、状态、结果、执行日志等完整信息
- **安全保存**：使用临时文件和原子性替换确保数据安全

### 2. **任务生命周期管理**
- **创建时**：自动生成任务配置文件
- **运行中**：实时更新进度和状态
- **暂停时**：保存当前状态和剩余工作
- **完成后**：记录最终结果和统计信息

### 3. **跨会话持久化**
- **程序退出**：任务配置自动保存
- **程序启动**：自动加载历史任务
- **状态恢复**：可以继续未完成的任务

## 📁 文件结构

### 任务配置文件格式
```json
{
  "task_id": "93ad26c4",
  "created_time": "2025-05-27T21:38:12.388477",
  "updated_time": "2025-05-27T21:38:12.470726",
  "status": "completed",
  "progress": {
    "total_stocks": 1000,
    "processed_stocks": 1000,
    "successful_stocks": 850,
    "progress_percentage": 100.0
  },
  "parameters": {
    "type": "preset",
    "name": "稳健策略",
    "short_window": 5,
    "long_window": 20,
    "data_dir": "../日线数据",
    "execution_price": "open",
    "use_enhanced_logic": true,
    "use_cache": true,
    "generate_charts": false,
    "save_interval": 20
  },
  "results": {
    "avg_return": 0.18,
    "win_rate": 0.68,
    "max_return": 0.52,
    "min_return": -0.18,
    "total_trades": 850
  },
  "execution_log": [
    {
      "timestamp": "2025-05-27T21:38:12.413671",
      "level": "info",
      "message": "开始处理股票数据"
    }
  ],
  "cache_info": {
    "cache_enabled": true,
    "cached_stocks": 0,
    "cache_directory": ""
  }
}
```

### 目录结构
```
Multi_strategy_V1.3/
├── tasks/                          # 任务配置目录
│   ├── task_93ad26c4.json         # 任务配置文件
│   └── task_a1fbfbc0.json         # 其他任务配置
├── 任务管理器.py                   # 任务管理器核心模块
├── 策略GUI优雅版.py                # 主界面程序
├── 策略工作进程_优雅版.py          # 工作进程
└── 测试程序/
    ├── 测试任务管理器.py           # 任务管理器测试
    └── 持久化任务管理功能说明.md   # 本说明文档
```

## 🚀 使用方法

### 1. **创建新任务**
1. 在策略GUI中配置参数
2. 点击"🚀 添加到队列"按钮
3. 系统自动创建任务配置文件
4. 任务开始执行并实时更新进度

### 2. **查看历史任务**
- **历史任务面板**：显示所有历史任务的状态和进度
- **任务信息**：包括任务ID、策略名称、状态、创建时间、进度、收益率等
- **状态标识**：
  - 🆕 已创建
  - 🔄 运行中
  - ⏸️ 已暂停
  - ✅ 已完成
  - ❌ 失败

### 3. **继续历史任务**
1. 在历史任务面板中选择要继续的任务
2. 点击"▶️ 继续选中任务"按钮
3. 系统加载任务配置并继续执行
4. 支持继续暂停的任务和失败的任务

### 4. **管理历史任务**
- **刷新列表**：点击"🔄 刷新历史任务"更新显示
- **删除任务**：点击"🗑️ 删除选中任务"清理不需要的任务
- **自动清理**：系统可以自动清理超过30天的已完成任务

## 🔧 技术实现

### 1. **任务管理器类 (TaskManager)**
```python
class TaskManager:
    def create_task(self, params) -> str:
        """创建新任务并生成配置文件"""
    
    def update_task_status(self, task_id, status, **kwargs):
        """更新任务状态"""
    
    def update_task_progress(self, task_id, total_stocks, processed_stocks, successful_stocks):
        """更新任务进度"""
    
    def add_execution_log(self, task_id, message, level):
        """添加执行日志"""
    
    def get_all_tasks(self) -> List[Dict]:
        """获取所有任务配置"""
```

### 2. **工作进程集成**
- 任务开始时更新状态为 'running'
- 处理过程中定期更新进度
- 完成时更新最终结果
- 失败时记录错误信息

### 3. **GUI界面集成**
- 历史任务面板显示所有任务
- 支持继续和删除操作
- 实时刷新任务状态

## 📊 任务状态说明

### 状态类型
- **created**：任务已创建，等待执行
- **running**：任务正在运行中
- **paused**：任务已暂停，可以恢复
- **completed**：任务已成功完成
- **failed**：任务执行失败

### 进度信息
- **total_stocks**：总股票数量
- **processed_stocks**：已处理股票数量
- **successful_stocks**：成功处理股票数量
- **progress_percentage**：完成百分比

### 结果统计
- **avg_return**：平均收益率
- **win_rate**：胜率
- **max_return**：最大收益率
- **min_return**：最小收益率
- **total_trades**：总交易次数

## 🛠️ 高级功能

### 1. **任务恢复机制**
- 支持从任意中断点恢复
- 自动加载已处理的缓存结果
- 只处理剩余未完成的股票

### 2. **日志系统**
- 记录任务执行的关键事件
- 支持不同日志级别 (info, warning, error)
- 自动限制日志条数避免文件过大

### 3. **缓存集成**
- 与实时缓存机制完美集成
- 记录缓存使用情况
- 支持缓存目录管理

## 🔍 故障排除

### 1. **任务配置文件损坏**
- 检查 `tasks/` 目录下的 JSON 文件
- 使用测试程序验证文件格式
- 手动删除损坏的配置文件

### 2. **任务无法继续**
- 确认任务状态是否支持继续
- 检查原始参数是否有效
- 查看执行日志了解失败原因

### 3. **历史任务显示异常**
- 点击"🔄 刷新历史任务"按钮
- 检查任务配置文件是否存在
- 重启程序重新加载

## 📈 性能优化

### 1. **文件操作优化**
- 使用临时文件确保原子性写入
- 批量更新减少文件IO次数
- 异步保存避免阻塞主线程

### 2. **内存管理**
- 限制执行日志条数
- 定期清理旧任务配置
- 按需加载任务详情

### 3. **界面响应**
- 异步刷新历史任务列表
- 分页显示大量任务
- 缓存任务状态减少重复查询

## 🔮 未来扩展

### 计划功能
- 任务优先级和调度
- 任务模板和批量创建
- 任务执行统计和报告
- 跨机器的任务同步
- 任务依赖关系管理

### 集成建议
- 与版本控制系统集成
- 支持任务配置的导入导出
- 添加任务执行时间预估
- 实现任务资源使用监控
