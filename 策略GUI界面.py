#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
双均线策略图形界面 - 多线程版本
支持所有原有功能的可视化操作
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
import queue
import os
import glob
import time
from datetime import datetime
import sys

# 导入策略模块
from 双均线策略_增强版 import MovingAverageStrategyEnhanced
from 策略配置 import get_strategy_config, PRESET_STRATEGIES

class StrategyGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("双均线策略系统 - 图形界面版")
        self.root.geometry("1200x800")
        
        # 设置窗口图标和样式
        self.setup_style()
        
        # 线程和队列管理
        self.thread_queue = queue.Queue()
        self.current_thread = None
        self.is_running = False
        
        # 策略实例
        self.current_strategy = None
        
        # 创建界面
        self.create_widgets()
        
        # 启动队列监控
        self.check_queue()
        
    def setup_style(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Header.TLabel', font=('Arial', 10, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        
    def create_widgets(self):
        """创建主界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 创建各个功能区域
        self.create_strategy_selection(main_frame)
        self.create_control_panel(main_frame)
        self.create_progress_panel(main_frame)
        self.create_results_panel(main_frame)
        self.create_log_panel(main_frame)
        
    def create_strategy_selection(self, parent):
        """创建策略选择区域"""
        # 策略选择框架
        strategy_frame = ttk.LabelFrame(parent, text="策略参数配置", padding="10")
        strategy_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        strategy_frame.columnconfigure(1, weight=1)
        
        # 策略类型选择
        ttk.Label(strategy_frame, text="策略类型:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.strategy_type = tk.StringVar(value="preset")
        ttk.Radiobutton(strategy_frame, text="预设策略", variable=self.strategy_type, 
                       value="preset", command=self.on_strategy_type_change).grid(row=0, column=1, sticky=tk.W)
        ttk.Radiobutton(strategy_frame, text="自定义参数", variable=self.strategy_type, 
                       value="custom", command=self.on_strategy_type_change).grid(row=0, column=2, sticky=tk.W)
        
        # 预设策略选择
        ttk.Label(strategy_frame, text="预设策略:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        
        self.preset_strategy = tk.StringVar()
        self.preset_combo = ttk.Combobox(strategy_frame, textvariable=self.preset_strategy, 
                                        values=list(PRESET_STRATEGIES.keys()), state="readonly")
        self.preset_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(10, 0), padx=(0, 10))
        self.preset_combo.set(list(PRESET_STRATEGIES.keys())[0])
        
        # 自定义参数
        custom_frame = ttk.Frame(strategy_frame)
        custom_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Label(custom_frame, text="短期均线:").grid(row=0, column=0, sticky=tk.W)
        self.short_window = tk.StringVar(value="5")
        ttk.Entry(custom_frame, textvariable=self.short_window, width=8).grid(row=0, column=1, padx=(5, 20))
        
        ttk.Label(custom_frame, text="长期均线:").grid(row=0, column=2, sticky=tk.W)
        self.long_window = tk.StringVar(value="20")
        ttk.Entry(custom_frame, textvariable=self.long_window, width=8).grid(row=0, column=3, padx=(5, 20))
        
        # 运行选项
        options_frame = ttk.Frame(strategy_frame)
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.use_cache = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="使用缓存", variable=self.use_cache).grid(row=0, column=0, sticky=tk.W)
        
        self.generate_charts = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text="生成图表", variable=self.generate_charts).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        ttk.Label(options_frame, text="保存间隔:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5))
        self.save_interval = tk.StringVar(value="20")
        ttk.Entry(options_frame, textvariable=self.save_interval, width=8).grid(row=0, column=3, padx=(0, 5))
        ttk.Label(options_frame, text="只股票").grid(row=0, column=4, sticky=tk.W)
        
        # 初始状态设置
        self.on_strategy_type_change()
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="运行控制", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10), padx=(0, 10))
        
        # 运行按钮
        self.run_button = ttk.Button(control_frame, text="开始运行策略", 
                                    command=self.run_strategy, style='Accent.TButton')
        self.run_button.grid(row=0, column=0, padx=(0, 10))
        
        # 停止按钮
        self.stop_button = ttk.Button(control_frame, text="停止运行", 
                                     command=self.stop_strategy, state='disabled')
        self.stop_button.grid(row=0, column=1, padx=(0, 10))
        
        # 缓存管理按钮
        ttk.Button(control_frame, text="缓存管理", 
                  command=self.open_cache_manager).grid(row=0, column=2, padx=(0, 10))
        
        # 收益率曲线按钮
        ttk.Button(control_frame, text="📈 多策略对比", 
                  command=self.open_multi_strategy_comparison).grid(row=0, column=3)
        
    def create_progress_panel(self, parent):
        """创建进度显示面板"""
        progress_frame = ttk.LabelFrame(parent, text="运行状态", padding="10")
        progress_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        # 状态标签
        self.status_label = ttk.Label(progress_frame, text="就绪", style='Header.TLabel')
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 进度条
        self.progress = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 详细信息
        self.detail_label = ttk.Label(progress_frame, text="")
        self.detail_label.grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        
    def create_results_panel(self, parent):
        """创建结果显示面板"""
        results_frame = ttk.LabelFrame(parent, text="运行结果", padding="10")
        results_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview显示结果
        columns = ('策略', '股票数', '平均收益率', '胜率', '最大收益', '最小收益')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=8)
        
        # 设置列标题
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=100, anchor='center')
        
        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        scrollbar_h = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        self.results_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
    def create_log_panel(self, parent):
        """创建日志输出面板"""
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 创建滚动文本区域
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清除日志按钮
        ttk.Button(log_frame, text="清除日志", 
                  command=lambda: self.log_text.delete(1.0, tk.END)).grid(row=1, column=0, pady=(5, 0))
        
    def on_strategy_type_change(self):
        """策略类型改变时的处理"""
        if self.strategy_type.get() == "preset":
            self.preset_combo.configure(state="readonly")
        else:
            self.preset_combo.configure(state="disabled")
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_status(self, status, detail=""):
        """更新状态显示"""
        self.status_label.config(text=status)
        self.detail_label.config(text=detail)
    
    def run_strategy(self):
        """运行策略（多线程）"""
        if self.is_running:
            return
        
        # 验证参数
        if not self.validate_parameters():
            return
        
        # 准备运行
        self.is_running = True
        self.run_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.progress.start()
        
        # 获取参数
        params = self.get_strategy_parameters()
        
        # 启动工作线程
        self.current_thread = threading.Thread(target=self.strategy_worker, args=(params,), daemon=True)
        self.current_thread.start()
        
        self.log_message("策略运行已启动...")
    
    def validate_parameters(self):
        """验证策略参数"""
        if self.strategy_type.get() == "custom":
            try:
                short = int(self.short_window.get())
                long = int(self.long_window.get())
                if short >= long or short < 1 or long < 1:
                    messagebox.showerror("参数错误", "短期均线必须小于长期均线，且都必须大于0")
                    return False
            except ValueError:
                messagebox.showerror("参数错误", "请输入有效的数字")
                return False
        
        try:
            save_interval = int(self.save_interval.get())
            if save_interval < 1:
                messagebox.showerror("参数错误", "保存间隔必须大于0")
                return False
        except ValueError:
            messagebox.showerror("参数错误", "保存间隔必须是有效数字")
            return False
        
        return True
    
    def get_strategy_parameters(self):
        """获取策略参数"""
        if self.strategy_type.get() == "preset":
            strategy_name = self.preset_strategy.get()
            config = get_strategy_config(strategy_name)
            return {
                'type': 'preset',
                'name': strategy_name,
                'short_window': config['short_window'],
                'long_window': config['long_window'],
                'data_dir': config['data_dir'],
                'use_cache': self.use_cache.get(),
                'generate_charts': self.generate_charts.get(),
                'save_interval': int(self.save_interval.get())
            }
        else:
            return {
                'type': 'custom',
                'short_window': int(self.short_window.get()),
                'long_window': int(self.long_window.get()),
                'data_dir': "日线数据",
                'use_cache': self.use_cache.get(),
                'generate_charts': self.generate_charts.get(),
                'save_interval': int(self.save_interval.get())
            }
    
    def strategy_worker(self, params):
        """策略运行工作线程"""
        try:
            # 创建策略实例
            strategy = MovingAverageStrategyEnhanced(
                short_window=params['short_window'],
                long_window=params['long_window'],
                data_dir=params['data_dir']
            )
            
            # 自定义输出重定向
            original_print = print
            def thread_print(*args, **kwargs):
                message = ' '.join(str(arg) for arg in args)
                self.thread_queue.put(('log', message))
            
            # 重定向print输出
            import builtins
            builtins.print = thread_print
            
            # 更新状态
            self.thread_queue.put(('status', '正在运行策略...', f"MA{params['short_window']}-MA{params['long_window']}"))
            
            # 运行策略
            strategy.run_strategy(
                generate_charts=params['generate_charts'],
                use_cache=params['use_cache'],
                save_interval=params['save_interval']
            )
            
            # 恢复原始print
            builtins.print = original_print
            
            # 计算结果统计
            if strategy.results:
                returns = [result['total_return'] for result in strategy.results.values()]
                avg_return = sum(returns) / len(returns)
                positive_count = len([r for r in returns if r > 0])
                win_rate = positive_count / len(returns)
                max_return = max(returns)
                min_return = min(returns)
                
                strategy_name = params.get('name', f"MA{params['short_window']}-MA{params['long_window']}")
                
                result_data = (
                    strategy_name,
                    len(returns),
                    f"{avg_return:.2%}",
                    f"{win_rate:.1%}",
                    f"{max_return:.2%}",
                    f"{min_return:.2%}"
                )
                
                self.thread_queue.put(('result', result_data))
                self.thread_queue.put(('strategy_instance', strategy))
            
            self.thread_queue.put(('complete', '策略运行完成'))
            
        except Exception as e:
            self.thread_queue.put(('error', f"策略运行失败: {str(e)}"))
    
    def stop_strategy(self):
        """停止策略运行"""
        self.is_running = False
        self.run_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress.stop()
        self.update_status("已停止")
        self.log_message("策略运行已停止")
    
    def check_queue(self):
        """检查线程队列中的消息"""
        try:
            while True:
                message_type, *args = self.thread_queue.get_nowait()
                
                if message_type == 'log':
                    self.log_message(args[0])
                elif message_type == 'status':
                    self.update_status(args[0], args[1] if len(args) > 1 else "")
                elif message_type == 'result':
                    self.results_tree.insert('', 'end', values=args[0])
                elif message_type == 'strategy_instance':
                    self.current_strategy = args[0]
                elif message_type == 'complete':
                    self.stop_strategy()
                    self.update_status("完成", "策略运行成功")
                    messagebox.showinfo("运行完成", args[0])
                elif message_type == 'error':
                    self.stop_strategy()
                    self.update_status("错误", "")
                    messagebox.showerror("运行错误", args[0])
                    
        except queue.Empty:
            pass
        
        # 继续检查
        self.root.after(100, self.check_queue)
    
    def open_cache_manager(self):
        """打开缓存管理窗口"""
        CacheManagerWindow(self.root)
    
    def open_multi_strategy_comparison(self):
        """打开多策略对比窗口"""
        from 多策略收益率对比 import MultiStrategyComparisonWindow
        MultiStrategyComparisonWindow(self.root)


class CacheManagerWindow:
    """缓存管理窗口"""
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("缓存管理")
        self.window.geometry("800x500")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.create_widgets()
        self.refresh_cache_list()
    
    def create_widgets(self):
        """创建缓存管理界面"""
        frame = ttk.Frame(self.window, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(frame, text="缓存文件管理", style='Title.TLabel').pack(pady=(0, 10))
        
        # 缓存列表
        columns = ('文件名', '策略参数', '股票数', '文件大小', '创建时间')
        self.cache_tree = ttk.Treeview(frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.cache_tree.heading(col, text=col)
            self.cache_tree.column(col, width=120, anchor='center')
        
        # 滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.cache_tree.yview)
        self.cache_tree.configure(yscrollcommand=scrollbar.set)
        
        self.cache_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮框架
        button_frame = ttk.Frame(self.window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="刷新", command=self.refresh_cache_list).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="删除选中", command=self.delete_selected).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清除所有", command=self.clear_all).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT)
    
    def refresh_cache_list(self):
        """刷新缓存列表"""
        # 清除现有项目
        for item in self.cache_tree.get_children():
            self.cache_tree.delete(item)
        
        # 扫描缓存文件
        cache_dir = "./cache"
        if not os.path.exists(cache_dir):
            return
        
        pkl_files = glob.glob(os.path.join(cache_dir, "*.pkl"))
        
        for pkl_file in pkl_files:
            file_name = os.path.basename(pkl_file)
            file_size = os.path.getsize(pkl_file) / 1024 / 1024  # MB
            create_time = datetime.fromtimestamp(os.path.getctime(pkl_file)).strftime("%Y-%m-%d %H:%M")
            
            # 解析策略参数
            if "strategy_cache_MA" in file_name:
                params = file_name.replace("strategy_cache_MA", "").replace(".pkl", "")
                if "_" in params:
                    parts = params.split("_")
                    if len(parts) >= 2:
                        short, long = parts[0], parts[1]
                        strategy_desc = f"MA{short}-MA{long}"
                        if len(parts) > 2:
                            suffix = "_".join(parts[2:])
                            strategy_desc += f"_{suffix}"
                    else:
                        strategy_desc = "未知参数"
                else:
                    strategy_desc = "未知参数"
            else:
                strategy_desc = "自定义策略"
            
            # 获取股票数量（需要加载pkl文件）
            try:
                import pickle
                with open(pkl_file, 'rb') as f:
                    cache_data = pickle.load(f)
                # 尝试不同的数据结构
                if isinstance(cache_data, dict):
                    if 'results' in cache_data:
                        stock_count = len(cache_data['results'])
                    else:
                        # 直接按股票代码统计
                        stock_count = len([k for k in cache_data.keys() if isinstance(k, str) and len(k) >= 6])
                else:
                    stock_count = "未知"
            except Exception as e:
                stock_count = f"错误: {str(e)}"
            
            self.cache_tree.insert('', 'end', values=(
                file_name, strategy_desc, stock_count, f"{file_size:.1f}MB", create_time
            ))
    
    def delete_selected(self):
        """删除选中的缓存文件"""
        selected = self.cache_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请选择要删除的缓存文件")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的缓存文件吗？"):
            for item in selected:
                file_name = self.cache_tree.item(item)['values'][0]
                file_path = os.path.join("./cache", file_name)
                try:
                    os.remove(file_path)
                except Exception as e:
                    messagebox.showerror("错误", f"删除文件失败: {str(e)}")
            
            self.refresh_cache_list()
    
    def clear_all(self):
        """清除所有缓存"""
        if messagebox.askyesno("确认", "确定要清除所有缓存文件吗？此操作不可恢复！"):
            cache_dir = "./cache"
            if os.path.exists(cache_dir):
                pkl_files = glob.glob(os.path.join(cache_dir, "*.pkl"))
                for pkl_file in pkl_files:
                    try:
                        os.remove(pkl_file)
                    except Exception as e:
                        messagebox.showerror("错误", f"删除文件失败: {str(e)}")
            
            self.refresh_cache_list()


class MultiStrategyComparisonWindow:
    """多策略对比窗口"""
    def __init__(self, parent, strategy_instance):
        self.strategy_instance = strategy_instance
        self.window = tk.Toplevel(parent)
        self.window.title("多策略对比")
        self.window.geometry("600x400")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.create_widgets()
        self.refresh_pkl_list()
    
    def create_widgets(self):
        """创建多策略对比界面"""
        frame = ttk.Frame(self.window, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(frame, text="多策略对比", style='Title.TLabel').pack(pady=(0, 20))
        
        # pkl文件选择
        pkl_frame = ttk.LabelFrame(frame, text="选择策略数据", padding="10")
        pkl_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(pkl_frame, text="可用策略:").pack(anchor=tk.W)
        
        # pkl列表
        self.pkl_listbox = tk.Listbox(pkl_frame, height=6)
        pkl_scroll = ttk.Scrollbar(pkl_frame, orient=tk.VERTICAL, command=self.pkl_listbox.yview)
        self.pkl_listbox.configure(yscrollcommand=pkl_scroll.set)
        
        self.pkl_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=(5, 0))
        pkl_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=(5, 0))
        
        # 参数设置
        param_frame = ttk.LabelFrame(frame, text="参数设置", padding="10")
        param_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(param_frame, text="随机选择股票数量:").pack(anchor=tk.W)
        self.stock_count = tk.StringVar(value="50")
        ttk.Entry(param_frame, textvariable=self.stock_count, width=15).pack(anchor=tk.W, pady=(5, 10))
        
        self.save_charts = tk.BooleanVar(value=True)
        ttk.Checkbutton(param_frame, text="保存图表", variable=self.save_charts).pack(anchor=tk.W)
        
        # 按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))
        
        ttk.Button(button_frame, text="生成对比", command=self.generate_comparison).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self.window.destroy).pack(side=tk.RIGHT)
    
    def refresh_pkl_list(self):
        """刷新pkl文件列表"""
        self.pkl_listbox.delete(0, tk.END)
        self.pkl_data = []
        
        cache_dir = "./cache"
        if not os.path.exists(cache_dir):
            return
        
        pkl_files = glob.glob(os.path.join(cache_dir, "strategy_cache_*.pkl"))
        
        for pkl_file in pkl_files:
            file_name = os.path.basename(pkl_file)
            
            # 解析策略参数
            if "strategy_cache_MA" in file_name:
                params = file_name.replace("strategy_cache_MA", "").replace(".pkl", "")
                if "_" in params:
                    parts = params.split("_")
                    if len(parts) >= 2:
                        short, long = parts[0], parts[1]
                        strategy_desc = f"MA{short}-MA{long}"
                        if len(parts) > 2:
                            suffix = "_".join(parts[2:])
                            strategy_desc += f"_{suffix}"
                    else:
                        strategy_desc = "未知参数"
                else:
                    strategy_desc = "未知参数"
            else:
                strategy_desc = "自定义策略"
            
            # 获取文件大小
            file_size = os.path.getsize(pkl_file) / 1024 / 1024
            
            display_text = f"{strategy_desc} - {file_name} ({file_size:.1f}MB)"
            self.pkl_listbox.insert(tk.END, display_text)
            self.pkl_data.append(pkl_file)
        
        # 默认选择第一个
        if self.pkl_data:
            self.pkl_listbox.selection_set(0)
    
    def generate_comparison(self):
        """生成多策略对比"""
        # 获取选中的pkl文件
        selection = self.pkl_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个策略文件")
            return
        
        pkl_file = self.pkl_data[selection[0]]
        
        # 验证股票数量
        try:
            stock_count = int(self.stock_count.get())
            if stock_count < 1:
                raise ValueError
        except ValueError:
            messagebox.showerror("错误", "请输入有效的股票数量")
            return
        
        # 在新线程中生成对比
        def generate_worker():
            try:
                # 解析文件名获取参数
                file_name = os.path.basename(pkl_file)
                if "strategy_cache_MA" in file_name:
                    params = file_name.replace("strategy_cache_MA", "").replace(".pkl", "")
                    if "_" in params:
                        short_window, long_window = map(int, params.split("_"))
                    else:
                        short_window, long_window = 5, 20
                else:
                    short_window, long_window = 5, 20
                
                # 创建策略实例
                strategy = MovingAverageStrategyEnhanced(
                    short_window=short_window,
                    long_window=long_window,
                    data_dir="日线数据"
                )
                
                # 指定缓存文件
                strategy.cache_file = pkl_file
                
                # 加载数据
                strategy.load_cache()
                
                if not strategy.detailed_results:
                    raise Exception("所选策略文件中没有可用数据")
                
                # 生成对比
                strategy.plot_multi_strategy_comparison(
                    stock_count=stock_count,
                    save_charts=self.save_charts.get()
                )
                
                self.window.after(0, lambda: messagebox.showinfo("成功", "多策略对比生成完成！"))
                
            except Exception as e:
                self.window.after(0, lambda: messagebox.showerror("错误", f"生成对比失败: {str(e)}"))
        
        # 启动工作线程
        threading.Thread(target=generate_worker, daemon=True).start()
        
        # 关闭对话框
        self.window.destroy()


class CurveDialogWindow:
    """收益率曲线对话框"""
    def __init__(self, parent, strategy_instance):
        self.strategy_instance = strategy_instance
        self.window = tk.Toplevel(parent)
        self.window.title("收益率曲线生成")
        self.window.geometry("600x400")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.create_widgets()
        self.refresh_pkl_list()
    
    def create_widgets(self):
        """创建收益率曲线界面"""
        frame = ttk.Frame(self.window, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(frame, text="随机N只股票平均收益率曲线", style='Title.TLabel').pack(pady=(0, 20))
        
        # pkl文件选择
        pkl_frame = ttk.LabelFrame(frame, text="选择策略数据", padding="10")
        pkl_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(pkl_frame, text="可用策略:").pack(anchor=tk.W)
        
        # pkl列表
        self.pkl_listbox = tk.Listbox(pkl_frame, height=6)
        pkl_scroll = ttk.Scrollbar(pkl_frame, orient=tk.VERTICAL, command=self.pkl_listbox.yview)
        self.pkl_listbox.configure(yscrollcommand=pkl_scroll.set)
        
        self.pkl_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=(5, 0))
        pkl_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=(5, 0))
        
        # 参数设置
        param_frame = ttk.LabelFrame(frame, text="参数设置", padding="10")
        param_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(param_frame, text="随机选择股票数量:").pack(anchor=tk.W)
        self.stock_count = tk.StringVar(value="50")
        ttk.Entry(param_frame, textvariable=self.stock_count, width=15).pack(anchor=tk.W, pady=(5, 10))
        
        self.save_charts = tk.BooleanVar(value=True)
        ttk.Checkbutton(param_frame, text="保存图表", variable=self.save_charts).pack(anchor=tk.W)
        
        # 按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))
        
        ttk.Button(button_frame, text="生成曲线", command=self.generate_curve).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self.window.destroy).pack(side=tk.RIGHT)
    
    def refresh_pkl_list(self):
        """刷新pkl文件列表"""
        self.pkl_listbox.delete(0, tk.END)
        self.pkl_data = []
        
        cache_dir = "./cache"
        if not os.path.exists(cache_dir):
            return
        
        pkl_files = glob.glob(os.path.join(cache_dir, "strategy_cache_*.pkl"))
        
        for pkl_file in pkl_files:
            file_name = os.path.basename(pkl_file)
            
            # 解析策略参数
            if "strategy_cache_MA" in file_name:
                params = file_name.replace("strategy_cache_MA", "").replace(".pkl", "")
                if "_" in params:
                    parts = params.split("_")
                    if len(parts) >= 2:
                        short, long = parts[0], parts[1]
                        strategy_desc = f"MA{short}-MA{long}"
                        if len(parts) > 2:
                            suffix = "_".join(parts[2:])
                            strategy_desc += f"_{suffix}"
                    else:
                        strategy_desc = "未知参数"
                else:
                    strategy_desc = "未知参数"
            else:
                strategy_desc = "自定义策略"
            
            # 获取文件大小
            file_size = os.path.getsize(pkl_file) / 1024 / 1024
            
            display_text = f"{strategy_desc} - {file_name} ({file_size:.1f}MB)"
            self.pkl_listbox.insert(tk.END, display_text)
            self.pkl_data.append(pkl_file)
        
        # 默认选择第一个
        if self.pkl_data:
            self.pkl_listbox.selection_set(0)
    
    def generate_curve(self):
        """生成收益率曲线"""
        # 获取选中的pkl文件
        selection = self.pkl_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请选择一个策略文件")
            return
        
        pkl_file = self.pkl_data[selection[0]]
        
        # 验证股票数量
        try:
            stock_count = int(self.stock_count.get())
            if stock_count < 1:
                raise ValueError
        except ValueError:
            messagebox.showerror("错误", "请输入有效的股票数量")
            return
        
        # 在新线程中生成曲线
        def generate_worker():
            try:
                # 解析文件名获取参数
                file_name = os.path.basename(pkl_file)
                if "strategy_cache_MA" in file_name:
                    params = file_name.replace("strategy_cache_MA", "").replace(".pkl", "")
                    if "_" in params:
                        short_window, long_window = map(int, params.split("_"))
                    else:
                        short_window, long_window = 5, 20
                else:
                    short_window, long_window = 5, 20
                
                # 创建策略实例
                strategy = MovingAverageStrategyEnhanced(
                    short_window=short_window,
                    long_window=long_window,
                    data_dir="日线数据"
                )
                
                # 指定缓存文件
                strategy.cache_file = pkl_file
                
                # 加载数据
                strategy.load_cache()
                
                if not strategy.detailed_results:
                    raise Exception("所选策略文件中没有可用数据")
                
                # 生成曲线
                strategy.plot_average_return_curve(
                    stock_count=stock_count,
                    save_charts=self.save_charts.get()
                )
                
                self.window.after(0, lambda: messagebox.showinfo("成功", "收益率曲线生成完成！"))
                
            except Exception as e:
                self.window.after(0, lambda: messagebox.showerror("错误", f"生成曲线失败: {str(e)}"))
        
        # 启动工作线程
        threading.Thread(target=generate_worker, daemon=True).start()
        
        # 关闭对话框
        self.window.destroy()


def main():
    """主函数"""
    root = tk.Tk()
    app = StrategyGUI(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if app.is_running:
            if messagebox.askokcancel("退出", "策略正在运行中，确定要退出吗？"):
                app.stop_strategy()
                root.destroy()
        else:
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main() 