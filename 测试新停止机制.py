#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试新的优雅停止和强制停止机制
"""

import os
import time
import threading
from datetime import datetime

def test_stop_mechanisms():
    """测试停止机制"""
    print("🧪 测试新的停止机制")
    print("=" * 60)
    
    # 模拟任务ID
    task_id = "test_12345678"
    
    print(f"📋 测试任务ID: {task_id}")
    print()
    
    # 测试1: 优雅停止机制
    print("🧪 测试1: 优雅停止机制")
    print("-" * 40)
    
    graceful_stop_flag_file = f"temp_graceful_stop_{task_id}.flag"
    
    # 创建优雅停止标志文件
    print(f"📝 创建优雅停止标志文件: {graceful_stop_flag_file}")
    with open(graceful_stop_flag_file, 'w') as f:
        f.write(f"graceful_stop_signal_{datetime.now().isoformat()}")
    
    # 检查文件是否存在
    if os.path.exists(graceful_stop_flag_file):
        print("✅ 优雅停止标志文件创建成功")
        print("   含义: 等待当前正在计算的股票完成后停止，并保存计算结果")
    else:
        print("❌ 优雅停止标志文件创建失败")
    
    # 清理
    try:
        os.remove(graceful_stop_flag_file)
        print("🧹 已清理优雅停止标志文件")
    except:
        pass
    
    print()
    
    # 测试2: 强制停止机制
    print("🧪 测试2: 强制停止机制")
    print("-" * 40)
    
    force_stop_flag_file = f"temp_force_stop_{task_id}.flag"
    
    # 创建强制停止标志文件
    print(f"📝 创建强制停止标志文件: {force_stop_flag_file}")
    with open(force_stop_flag_file, 'w') as f:
        f.write(f"force_stop_signal_{datetime.now().isoformat()}")
    
    # 检查文件是否存在
    if os.path.exists(force_stop_flag_file):
        print("✅ 强制停止标志文件创建成功")
        print("   含义: 立即停止当前股票计算，无需保存当前股票结果")
    else:
        print("❌ 强制停止标志文件创建失败")
    
    # 清理
    try:
        os.remove(force_stop_flag_file)
        print("🧹 已清理强制停止标志文件")
    except:
        pass
    
    print()
    
    # 测试3: 模拟工作进程的停止检查逻辑
    print("🧪 测试3: 模拟工作进程的停止检查逻辑")
    print("-" * 40)
    
    def check_graceful_stop_signal():
        """检查优雅停止信号文件"""
        return os.path.exists(graceful_stop_flag_file)
    
    def check_force_stop_signal():
        """检查强制停止信号文件"""
        return os.path.exists(force_stop_flag_file)
    
    def cleanup_stop_flags():
        """清理停止标志文件"""
        try:
            if os.path.exists(graceful_stop_flag_file):
                os.remove(graceful_stop_flag_file)
            if os.path.exists(force_stop_flag_file):
                os.remove(force_stop_flag_file)
        except:
            pass
    
    # 模拟股票处理循环
    print("📊 模拟股票处理循环...")
    
    stocks = ["000001", "000002", "000003", "000004", "000005"]
    processed_count = 0
    
    for i, stock_code in enumerate(stocks, 1):
        print(f"   正在处理股票 ({i}/{len(stocks)}): {stock_code}")
        
        # 在开始处理每只股票前检查强制停止信号
        if check_force_stop_signal():
            print(f"   🛑 收到强制停止信号，立即停止（已处理 {processed_count} 只股票）")
            cleanup_stop_flags()
            break
        
        # 在开始处理每只股票前检查优雅停止信号
        graceful_stop_requested = check_graceful_stop_signal()
        if graceful_stop_requested:
            print(f"   ⏸️ 收到优雅停止信号，等待当前股票处理完成后停止")
        
        # 模拟股票处理时间
        time.sleep(0.1)
        
        # 模拟处理完成
        processed_count += 1
        print(f"   ✅ {stock_code} 处理完成，结果已保存")
        
        # 在当前股票处理完成后，检查是否收到优雅停止信号
        if graceful_stop_requested:
            print(f"   ⏸️ 当前股票 {stock_code} 处理完成，响应优雅停止信号")
            print(f"   ✅ 已保存 {processed_count} 只股票的计算结果")
            cleanup_stop_flags()
            break
    
    print(f"📈 模拟处理完成，共处理了 {processed_count} 只股票")
    print()
    
    # 测试4: 模拟GUI发送停止信号
    print("🧪 测试4: 模拟GUI发送停止信号")
    print("-" * 40)
    
    def simulate_graceful_stop():
        """模拟GUI发送优雅停止信号"""
        time.sleep(0.2)  # 等待一下再发送信号
        print("   📡 GUI发送优雅停止信号...")
        with open(graceful_stop_flag_file, 'w') as f:
            f.write(f"graceful_stop_signal_{datetime.now().isoformat()}")
    
    def simulate_force_stop():
        """模拟GUI发送强制停止信号"""
        time.sleep(0.1)  # 更快发送强制停止信号
        print("   📡 GUI发送强制停止信号...")
        with open(force_stop_flag_file, 'w') as f:
            f.write(f"force_stop_signal_{datetime.now().isoformat()}")
    
    # 测试优雅停止
    print("🔄 测试优雅停止场景:")
    thread = threading.Thread(target=simulate_graceful_stop)
    thread.start()
    
    # 模拟处理
    for i, stock_code in enumerate(["600000", "600001", "600002"], 1):
        if check_force_stop_signal():
            print(f"   🛑 强制停止")
            break
        if check_graceful_stop_signal():
            print(f"   ⏸️ 优雅停止，完成当前股票 {stock_code} 后停止")
            time.sleep(0.1)  # 模拟完成当前股票
            print(f"   ✅ {stock_code} 处理完成，停止执行")
            break
        time.sleep(0.1)
        print(f"   ✅ {stock_code} 处理完成")
    
    thread.join()
    cleanup_stop_flags()
    
    print()
    print("✅ 新停止机制测试完成！")
    print("=" * 60)
    print("🎯 测试结果总结:")
    print("  ✅ 优雅停止: 等待当前股票处理完成后停止，保存结果")
    print("  ✅ 强制停止: 立即停止当前股票计算，不保存当前股票")
    print("  ✅ 文件标志: 使用独立的标志文件区分两种停止方式")
    print("  ✅ 清理机制: 停止后自动清理标志文件")

if __name__ == "__main__":
    test_stop_mechanisms()
